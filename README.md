# نظام إدارة العقود والطبليات

نظام إدارة العقود والطبليات هو تطبيق ويب تفاعلي مصمم خصيصًا لتلبية احتياجات الجمعيات التعاونية في تنظيم عقود الموردين، إدارة الطبليات (الحاويات)، وتوثيق كل العمليات المرتبطة بالتأجير والخصومات والدعم.

## الخصائص الرئيسية

### 1. واجهة سهلة وذكية
- تصميم عصري باستخدام TailwindCSS
- يدعم جميع الشاشات (كمبيوتر، لابتوب، تابلت، موبايل)
- واجهة مستخدم تدعم اللغة العربية من اليمين لليسار (RTL)
- نماذج وجداول مرتبة وواضحة

### 2. إضافة وإدارة العقود
نموذج متكامل لإضافة عقد جديد، يتضمن:
- **رقم العقد**: يدخل يدويًا مع التحقق من عدم التكرار
- **اسم المورد**: يُختار من قائمة منسدلة بجميع الموردين المسجلين
- **عدد الطبليات**: يدخل رقم
- **عدد العيون**: يُحسب تلقائيًا (كل طبلية = 4 عيون)
- **قيمة إيجار العين**: ثابتة (60 دينار كويتي)
- **قيمة الإيجار الكلية**: تُحسب تلقائيًا (عدد العيون × 60)
- **نسبة الخصم**: يدخل رقم مئوي
- **القيمة النهائية**: تُحسب تلقائيًا بعد الخصم
- **تواريخ بداية ونهاية العقد**: يتم اختيارها من التقويم
- **ملاحظات إضافية**: حسب الحاجة

### 3. الجداول الديناميكية
عرض جميع العقود في جدول واحد، مع إمكانية استعراض:
- رقم العقد
- اسم المورد
- عدد الطبليات
- عدد العيون
- قيمة إيجار العين
- إجمالي قيمة العقد
- نسبة الخصم
- القيمة النهائية بعد الخصم
- تواريخ العقد
- الملاحظات
- إجراءات (تعديل، حذف، عرض)

### 4. حسابات تلقائية دقيقة
- النظام يحسب تلقائيًا عدد العيون وقيمة الإيجار الإجمالية
- يعرض قيمة الخصم ويُظهر القيمة النهائية للعقد بشكل فوري
- تحديث فوري للحسابات عند تغيير أي قيمة

### 5. إدارة الموردين
- إضافة موردين جدد
- تعديل بيانات الموردين الموجودين
- حذف الموردين غير المستخدمين
- عرض جميع بيانات الموردين (الاسم، الهاتف، البريد الإلكتروني، العنوان)

### 6. البحث والتنظيم
- بحث سريع في جميع العقود
- فلترة النتائج حسب أي معيار
- إحصائيات شاملة (إجمالي العقود، الطبليات، العيون، القيمة)

### 7. التصدير والطباعة
- تصدير البيانات إلى ملف Excel/CSV
- طباعة التقارير
- حفظ البيانات محليًا في المتصفح

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3 + TailwindCSS**: التصميم والتنسيق
- **JavaScript (ES6+)**: الوظائف التفاعلية
- **Font Awesome**: الأيقونات
- **Google Fonts (Tajawal)**: الخط العربي
- **LocalStorage**: حفظ البيانات محليًا

## كيفية الاستخدام

### 1. تشغيل النظام
1. افتح ملف `index.html` في أي متصفح حديث
2. النظام جاهز للاستخدام فورًا

### 2. إضافة مورد جديد
1. انتقل إلى تبويب "إدارة الموردين"
2. اضغط على "إضافة مورد جديد"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### 3. إضافة عقد جديد
1. انتقل إلى تبويب "إضافة عقد جديد"
2. املأ جميع البيانات المطلوبة
3. سيتم حساب العيون والقيم تلقائيًا
4. اضغط "حفظ العقد"

### 4. إدارة العقود
1. في تبويب "عرض العقود" يمكنك:
   - عرض جميع العقود
   - البحث في العقود
   - تعديل أو حذف العقود
   - عرض تفاصيل العقد
   - تصدير البيانات

## الميزات المتقدمة

### الحسابات التلقائية
- **عدد العيون** = عدد الطبليات × 4
- **إجمالي الإيجار** = عدد العيون × 60 د.ك
- **قيمة الخصم** = إجمالي الإيجار × (نسبة الخصم ÷ 100)
- **القيمة النهائية** = إجمالي الإيجار - قيمة الخصم

### الإحصائيات
- إجمالي عدد العقود
- إجمالي عدد الطبليات
- إجمالي عدد العيون
- إجمالي القيمة المالية

### التحقق من البيانات
- التأكد من عدم تكرار أرقام العقود
- التحقق من صحة التواريخ
- التحقق من صحة البيانات المدخلة

## متطلبات النظام

- متصفح حديث يدعم HTML5 و CSS3 و JavaScript
- اتصال بالإنترنت (لتحميل الخطوط والأيقونات)
- لا يتطلب خادم ويب أو قاعدة بيانات

## الأمان وحفظ البيانات

- البيانات تُحفظ محليًا في متصفح المستخدم
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- يُنصح بعمل نسخ احتياطية دورية عبر التصدير

## الدعم والتطوير

النظام قابل للتطوير والتخصيص حسب احتياجات الجمعية. يمكن إضافة المزيد من الميزات مثل:
- تقارير مفصلة
- إشعارات انتهاء العقود
- ربط مع قواعد بيانات خارجية
- نظام مستخدمين متعدد
- تطبيق موبايل

## الترخيص

هذا النظام مطور خصيصًا للجمعيات التعاونية ويمكن استخدامه وتعديله حسب الحاجة.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025  
**الإصدار**: 1.0
