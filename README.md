# نظام إدارة العقود والموردين

نظام شامل لإدارة عقود الموردين والطبليات في الجمعيات التعاونية الكويتية مع قاعدة بيانات موردين البهارات.

## المميزات الجديدة 🆕

### قاعدة بيانات موردين البهارات
- **قائمة شاملة**: 20 مورد بهارات في الكويت
- **معلومات مفصلة**: أسماء، أرقام هواتف، عناوين بريد إلكتروني، مواقع
- **تحميل سريع**: إضافة جميع موردين البهارات بضغطة واحدة
- **تجنب التكرار**: النظام يتجنب إضافة موردين موجودين مسبقاً

### إدارة متقدمة للموردين
- **تصدير الموردين**: حفظ قائمة الموردين بصيغة JSON
- **استيراد الموردين**: تحميل موردين من ملفات JSON خارجية
- **دعم صيغ متعددة**: يدعم صيغ JSON مختلفة للاستيراد

## قائمة موردين البهارات المدمجة

النظام يحتوي على قائمة جاهزة من 20 مورد بهارات في الكويت:

1. **مورد البهارات الذهبية** - سوق المباركية
2. **شركة بهارات الخليج** - السوق المركزي، حولي
3. **مؤسسة التوابل الكويتية** - سوق الجمعة، الفروانية
4. **بهارات الشرق الأوسط** - سوق الأحمدي
5. **مورد التوابل الهندية** - منطقة الجهراء
6. **شركة البهارات العربية** - سوق الصفاة
7. **مؤسسة بهارات الأصالة** - مبارك الكبير
8. **تجارة التوابل الدولية** - منطقة العاصمة
9. **بهارات السلطان** - سوق الخضار، حولي
10. **مورد البهارات الطبيعية** - الفروانية

... و 10 موردين إضافيين

## الخصائص الرئيسية

### 1. واجهة سهلة وذكية
- تصميم عصري باستخدام TailwindCSS
- يدعم جميع الشاشات (كمبيوتر، لابتوب، تابلت، موبايل)
- واجهة مستخدم تدعم اللغة العربية من اليمين لليسار (RTL)
- نماذج وجداول مرتبة وواضحة

### 2. إضافة وإدارة العقود
نموذج متكامل لإضافة عقد جديد، يتضمن:
- **رقم العقد**: يدخل يدويًا مع التحقق من عدم التكرار
- **اسم المورد**: يُختار من قائمة منسدلة بجميع الموردين المسجلين
- **عدد الطبليات**: يدخل رقم
- **عدد العيون**: يُحسب تلقائيًا (كل طبلية = 4 عيون)
- **قيمة إيجار العين**: ثابتة (60 دينار كويتي)
- **قيمة الإيجار الكلية**: تُحسب تلقائيًا (عدد العيون × 60)
- **نسبة الخصم**: يدخل رقم مئوي
- **القيمة النهائية**: تُحسب تلقائيًا بعد الخصم
- **تواريخ بداية ونهاية العقد**: يتم اختيارها من التقويم
- **ملاحظات إضافية**: حسب الحاجة

### 3. الجداول الديناميكية
عرض جميع العقود في جدول واحد، مع إمكانية استعراض:
- رقم العقد
- اسم المورد
- عدد الطبليات
- عدد العيون
- قيمة إيجار العين
- إجمالي قيمة العقد
- نسبة الخصم
- القيمة النهائية بعد الخصم
- تواريخ العقد
- الملاحظات
- إجراءات (تعديل، حذف، عرض)

### 4. حسابات تلقائية دقيقة
- النظام يحسب تلقائيًا عدد العيون وقيمة الإيجار الإجمالية
- يعرض قيمة الخصم ويُظهر القيمة النهائية للعقد بشكل فوري
- تحديث فوري للحسابات عند تغيير أي قيمة

### 5. إدارة الموردين
- إضافة موردين جدد
- تعديل بيانات الموردين الموجودين
- حذف الموردين غير المستخدمين
- عرض جميع بيانات الموردين (الاسم، الهاتف، البريد الإلكتروني، العنوان)
- **جديد**: تحميل قائمة موردين البهارات الجاهزة
- **جديد**: تصدير واستيراد قوائم الموردين

### 6. البحث والتنظيم
- بحث سريع في جميع العقود
- فلترة النتائج حسب أي معيار
- إحصائيات شاملة (إجمالي العقود، الطبليات، العيون، القيمة)

### 7. التصدير والطباعة
- تصدير البيانات إلى ملف Excel/CSV
- **جديد**: تصدير قائمة الموردين إلى JSON
- طباعة التقارير
- حفظ البيانات محليًا في المتصفح

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3 + TailwindCSS**: التصميم والتنسيق
- **JavaScript (ES6+)**: الوظائف التفاعلية
- **Font Awesome**: الأيقونات
- **Google Fonts (Tajawal)**: الخط العربي
- **LocalStorage**: حفظ البيانات محليًا

## كيفية الاستخدام

### 1. تشغيل النظام
1. افتح ملف `index.html` في أي متصفح حديث
2. النظام جاهز للاستخدام فورًا

### 2. تحميل موردين البهارات 🌶️
1. انتقل إلى تبويب "إدارة الموردين"
2. اضغط على زر "تحميل موردين البهارات" (الأخضر)
3. أكد العملية في النافذة المنبثقة
4. سيتم إضافة جميع موردين البهارات الجدد تلقائياً

### 3. تصدير واستيراد الموردين
**للتصدير:**
1. اضغط زر "تصدير الموردين" (البنفسجي)
2. سيتم تحميل ملف JSON يحتوي على جميع الموردين

**للاستيراد:**
1. اضغط زر "استيراد موردين" (البرتقالي)
2. اختر ملف JSON يحتوي على قائمة موردين
3. سيتم إضافة الموردين الجدد فقط

### 4. إضافة مورد جديد
1. انتقل إلى تبويب "إدارة الموردين"
2. اضغط على "إضافة مورد جديد"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### 5. إضافة عقد جديد
1. انتقل إلى تبويب "إضافة عقد جديد"
2. املأ جميع البيانات المطلوبة
3. سيتم حساب العيون والقيم تلقائيًا
4. اضغط "حفظ العقد"

### 6. إدارة العقود
1. في تبويب "عرض العقود" يمكنك:
   - عرض جميع العقود
   - البحث في العقود
   - تعديل أو حذف العقود
   - عرض تفاصيل العقد
   - تصدير البيانات

## الميزات المتقدمة

### الحسابات التلقائية
- **عدد العيون** = عدد الطبليات × 4
- **إجمالي الإيجار** = عدد العيون × 60 د.ك
- **قيمة الخصم** = إجمالي الإيجار × (نسبة الخصم ÷ 100)
- **القيمة النهائية** = إجمالي الإيجار - قيمة الخصم

### الإحصائيات
- إجمالي عدد العقود
- إجمالي عدد الطبليات
- إجمالي عدد العيون
- إجمالي القيمة المالية

### التحقق من البيانات
- التأكد من عدم تكرار أرقام العقود
- التحقق من صحة التواريخ
- التحقق من صحة البيانات المدخلة

## متطلبات النظام

- متصفح حديث يدعم HTML5 و CSS3 و JavaScript
- اتصال بالإنترنت (لتحميل الخطوط والأيقونات)
- لا يتطلب خادم ويب أو قاعدة بيانات

## الملفات المرفقة

- `index.html` - الملف الرئيسي للنظام (متكامل)
- `موردين_البهارات.json` - قاعدة بيانات موردين البهارات
- `README.md` - دليل الاستخدام

## الأمان وحفظ البيانات

- البيانات تُحفظ محليًا في متصفح المستخدم
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- يُنصح بعمل نسخ احتياطية دورية عبر التصدير
- استخدم خاصية "تصدير الموردين" لحفظ نسخة احتياطية من الموردين

## الدعم والتطوير

النظام قابل للتطوير والتخصيص حسب احتياجات الجمعية. يمكن إضافة المزيد من الميزات مثل:
- تقارير مفصلة
- إشعارات انتهاء العقود
- ربط مع قواعد بيانات خارجية
- نظام مستخدمين متعدد
- تطبيق موبايل

## الترخيص

هذا النظام مطور خصيصًا للجمعيات التعاونية ويمكن استخدامه وتعديله حسب الحاجة.

---

**تم التطوير خصيصاً للجمعيات التعاونية في دولة الكويت**
**محدث بقاعدة بيانات شاملة لموردين البهارات** 🌶️
