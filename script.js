// Global variables
let contracts = [];
let suppliers = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupEventListeners();
    updateStatistics();
    populateSupplierDropdown();
    displayContracts();
    displaySuppliers();
});

// Setup event listeners
function setupEventListeners() {
    // Tab switching
    document.getElementById('contractsTab').addEventListener('click', () => switchTab('contracts'));
    document.getElementById('addContractTab').addEventListener('click', () => switchTab('addContract'));
    document.getElementById('suppliersTab').addEventListener('click', () => switchTab('suppliers'));
    
    // Form submissions
    document.getElementById('contractForm').addEventListener('submit', handleContractSubmit);
    document.getElementById('supplierForm').addEventListener('submit', handleSupplierSubmit);
    
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', filterContracts);
}

// Tab switching functionality
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('text-gray-600');
    });
    
    // Show selected tab content
    document.getElementById(tabName + 'Content').classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + 'Tab');
    activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeTab.classList.remove('text-gray-600');
}

// Load data from localStorage
function loadData() {
    const savedContracts = localStorage.getItem('contracts');
    const savedSuppliers = localStorage.getItem('suppliers');
    
    if (savedContracts) {
        contracts = JSON.parse(savedContracts);
    }
    
    if (savedSuppliers) {
        suppliers = JSON.parse(savedSuppliers);
    } else {
        // Add some default suppliers
        suppliers = [
            { id: 1, name: 'شركة الخليج للتجارة', phone: '99887766', email: '<EMAIL>', address: 'الكويت' },
            { id: 2, name: 'مؤسسة النور التجارية', phone: '99776655', email: '<EMAIL>', address: 'حولي' },
            { id: 3, name: 'شركة الأمل للمواد الغذائية', phone: '99665544', email: '<EMAIL>', address: 'الفروانية' }
        ];
        saveData();
    }
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('contracts', JSON.stringify(contracts));
    localStorage.setItem('suppliers', JSON.stringify(suppliers));
}

// Calculate totals when tables or discount changes
function calculateTotals() {
    const numberOfTables = parseInt(document.getElementById('numberOfTables').value) || 0;
    const discountPercentage = parseFloat(document.getElementById('discountPercentage').value) || 0;
    const rentPerEye = 60; // Fixed value
    
    // Calculate number of eyes (4 eyes per table)
    const numberOfEyes = numberOfTables * 4;
    document.getElementById('numberOfEyes').value = numberOfEyes;
    
    // Calculate total rent
    const totalRent = numberOfEyes * rentPerEye;
    document.getElementById('totalRent').value = totalRent;
    
    // Calculate final amount after discount
    const discountAmount = (totalRent * discountPercentage) / 100;
    const finalAmount = totalRent - discountAmount;
    document.getElementById('finalAmount').value = finalAmount.toFixed(2);
}

// Handle contract form submission
function handleContractSubmit(e) {
    e.preventDefault();
    
    const formData = {
        id: Date.now(),
        contractNumber: document.getElementById('contractNumber').value,
        supplierName: document.getElementById('supplierName').value,
        numberOfTables: parseInt(document.getElementById('numberOfTables').value),
        numberOfEyes: parseInt(document.getElementById('numberOfEyes').value),
        rentPerEye: 60,
        totalRent: parseFloat(document.getElementById('totalRent').value),
        discountPercentage: parseFloat(document.getElementById('discountPercentage').value) || 0,
        finalAmount: parseFloat(document.getElementById('finalAmount').value),
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        notes: document.getElementById('notes').value
    };
    
    // Validate contract number uniqueness
    if (contracts.some(contract => contract.contractNumber === formData.contractNumber)) {
        alert('رقم العقد موجود مسبقاً. يرجى استخدام رقم مختلف.');
        return;
    }
    
    contracts.push(formData);
    saveData();
    displayContracts();
    updateStatistics();
    resetForm();
    
    alert('تم حفظ العقد بنجاح!');
    switchTab('contracts');
}

// Handle supplier form submission
function handleSupplierSubmit(e) {
    e.preventDefault();
    
    const formData = {
        id: Date.now(),
        name: document.getElementById('supplierNameInput').value,
        phone: document.getElementById('supplierPhone').value,
        email: document.getElementById('supplierEmail').value,
        address: document.getElementById('supplierAddress').value
    };
    
    suppliers.push(formData);
    saveData();
    displaySuppliers();
    populateSupplierDropdown();
    hideAddSupplierModal();
    
    alert('تم إضافة المورد بنجاح!');
}

// Reset contract form
function resetForm() {
    document.getElementById('contractForm').reset();
    document.getElementById('numberOfEyes').value = '';
    document.getElementById('totalRent').value = '';
    document.getElementById('finalAmount').value = '';
    document.getElementById('rentPerEye').value = 60;
}

// Display contracts in table
function displayContracts() {
    const tbody = document.getElementById('contractsTableBody');
    tbody.innerHTML = '';
    
    contracts.forEach(contract => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';
        
        row.innerHTML = `
            <td class="border border-gray-300 px-4 py-3">${contract.contractNumber}</td>
            <td class="border border-gray-300 px-4 py-3">${contract.supplierName}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">${contract.numberOfTables}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">${contract.numberOfEyes}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">${contract.rentPerEye} د.ك</td>
            <td class="border border-gray-300 px-4 py-3 text-center">${contract.totalRent} د.ك</td>
            <td class="border border-gray-300 px-4 py-3 text-center">${contract.discountPercentage}%</td>
            <td class="border border-gray-300 px-4 py-3 text-center font-semibold text-green-600">${contract.finalAmount} د.ك</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(contract.startDate)}</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(contract.endDate)}</td>
            <td class="border border-gray-300 px-4 py-3">
                <div class="flex space-x-2 space-x-reverse">
                    <button onclick="editContract(${contract.id})" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteContract(${contract.id})" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button onclick="viewContract(${contract.id})" class="text-green-600 hover:text-green-800">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Display suppliers in table
function displaySuppliers() {
    const tbody = document.getElementById('suppliersTableBody');
    tbody.innerHTML = '';
    
    suppliers.forEach(supplier => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';
        
        row.innerHTML = `
            <td class="border border-gray-300 px-4 py-3">${supplier.name}</td>
            <td class="border border-gray-300 px-4 py-3">${supplier.phone || '-'}</td>
            <td class="border border-gray-300 px-4 py-3">${supplier.email || '-'}</td>
            <td class="border border-gray-300 px-4 py-3">${supplier.address || '-'}</td>
            <td class="border border-gray-300 px-4 py-3">
                <div class="flex space-x-2 space-x-reverse">
                    <button onclick="editSupplier(${supplier.id})" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Populate supplier dropdown
function populateSupplierDropdown() {
    const select = document.getElementById('supplierName');
    select.innerHTML = '<option value="">اختر المورد</option>';
    
    suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.name;
        option.textContent = supplier.name;
        select.appendChild(option);
    });
}

// Update statistics
function updateStatistics() {
    const totalContracts = contracts.length;
    const totalTables = contracts.reduce((sum, contract) => sum + contract.numberOfTables, 0);
    const totalEyes = contracts.reduce((sum, contract) => sum + contract.numberOfEyes, 0);
    const totalValue = contracts.reduce((sum, contract) => sum + contract.finalAmount, 0);
    
    document.getElementById('totalContracts').textContent = totalContracts;
    document.getElementById('totalTables').textContent = totalTables;
    document.getElementById('totalEyes').textContent = totalEyes;
    document.getElementById('totalValue').textContent = totalValue.toFixed(2) + ' د.ك';
}

// Filter contracts based on search input
function filterContracts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('#contractsTableBody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// Modal functions
function showAddSupplierModal() {
    document.getElementById('supplierModal').classList.remove('hidden');
    document.getElementById('supplierModal').classList.add('flex');
}

function hideAddSupplierModal() {
    document.getElementById('supplierModal').classList.add('hidden');
    document.getElementById('supplierModal').classList.remove('flex');
    document.getElementById('supplierForm').reset();
}

// Contract actions
function editContract(id) {
    const contract = contracts.find(c => c.id === id);
    if (contract) {
        // Populate form with contract data
        document.getElementById('contractNumber').value = contract.contractNumber;
        document.getElementById('supplierName').value = contract.supplierName;
        document.getElementById('numberOfTables').value = contract.numberOfTables;
        document.getElementById('discountPercentage').value = contract.discountPercentage;
        document.getElementById('startDate').value = contract.startDate;
        document.getElementById('endDate').value = contract.endDate;
        document.getElementById('notes').value = contract.notes;
        
        calculateTotals();
        
        // Remove the contract from array (will be re-added when form is submitted)
        contracts = contracts.filter(c => c.id !== id);
        saveData();
        
        switchTab('addContract');
    }
}

function deleteContract(id) {
    if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
        contracts = contracts.filter(c => c.id !== id);
        saveData();
        displayContracts();
        updateStatistics();
    }
}

function viewContract(id) {
    const contract = contracts.find(c => c.id === id);
    if (contract) {
        alert(`تفاصيل العقد:
رقم العقد: ${contract.contractNumber}
المورد: ${contract.supplierName}
عدد الطبليات: ${contract.numberOfTables}
عدد العيون: ${contract.numberOfEyes}
القيمة النهائية: ${contract.finalAmount} د.ك
تاريخ البداية: ${formatDate(contract.startDate)}
تاريخ النهاية: ${formatDate(contract.endDate)}
${contract.notes ? 'ملاحظات: ' + contract.notes : ''}`);
    }
}

// Supplier actions
function editSupplier(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (supplier) {
        document.getElementById('supplierNameInput').value = supplier.name;
        document.getElementById('supplierPhone').value = supplier.phone;
        document.getElementById('supplierEmail').value = supplier.email;
        document.getElementById('supplierAddress').value = supplier.address;
        
        // Remove the supplier from array (will be re-added when form is submitted)
        suppliers = suppliers.filter(s => s.id !== id);
        saveData();
        
        showAddSupplierModal();
    }
}

function deleteSupplier(id) {
    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
        suppliers = suppliers.filter(s => s.id !== id);
        saveData();
        displaySuppliers();
        populateSupplierDropdown();
    }
}

// Export to Excel functionality
function exportToExcel() {
    if (contracts.length === 0) {
        alert('لا توجد عقود للتصدير');
        return;
    }
    
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "رقم العقد,اسم المورد,عدد الطبليات,عدد العيون,قيمة إيجار العين,إجمالي القيمة,نسبة الخصم,القيمة بعد الخصم,تاريخ البداية,تاريخ النهاية,ملاحظات\n";
    
    contracts.forEach(contract => {
        const row = [
            contract.contractNumber,
            contract.supplierName,
            contract.numberOfTables,
            contract.numberOfEyes,
            contract.rentPerEye,
            contract.totalRent,
            contract.discountPercentage + '%',
            contract.finalAmount,
            contract.startDate,
            contract.endDate,
            contract.notes || ''
        ].join(',');
        csvContent += row + "\n";
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "contracts_" + new Date().toISOString().split('T')[0] + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
