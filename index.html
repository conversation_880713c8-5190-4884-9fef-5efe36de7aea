<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقود والطبليات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['<PERSON><PERSON><PERSON>', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Additional custom styles for the Contract Management System */

        /* RTL Support */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
        }

        /* Custom animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Hover effects */
        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Tab styling */
        .tab-button {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }

        .tab-button.active {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .tab-button:hover:not(.active) {
            background-color: rgba(59, 130, 246, 0.05);
            border-bottom-color: rgba(59, 130, 246, 0.3);
        }

        /* Form styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input:disabled {
            background-color: #f9fafb;
            color: #6b7280;
        }

        /* Button styling */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
        }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        /* Table styling */
        .table-container {
            overflow-x: auto;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
        }

        .data-table th {
            background-color: #f9fafb;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            color: #374151;
        }

        .data-table tr:hover {
            background-color: #f9fafb;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* Statistics cards */
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stat-card-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .stat-card-green {
            background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        }

        .stat-card-yellow {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-card-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* Modal styling */
        .modal-overlay {
            backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            animation: slideIn 0.3s ease;
            max-height: 90vh;
            overflow-y: auto;
        }

        /* Search input styling */
        .search-input {
            position: relative;
        }

        .search-input::before {
            content: '\f002';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            pointer-events: none;
        }

        .search-input input {
            padding-left: 2.5rem;
        }

        /* Action buttons */
        .action-btn {
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn-edit {
            color: #3b82f6;
        }

        .action-btn-edit:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .action-btn-delete {
            color: #ef4444;
        }

        .action-btn-delete:hover {
            background-color: rgba(239, 68, 68, 0.1);
        }

        .action-btn-view {
            color: #10b981;
        }

        .action-btn-view:hover {
            background-color: rgba(16, 185, 129, 0.1);
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .data-table {
                font-size: 0.875rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .modal-content {
                margin: 1rem;
                max-width: calc(100% - 2rem);
            }
        }

        @media (max-width: 640px) {
            .grid-cols-1 {
                grid-template-columns: 1fr;
            }

            .md\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .md\\:grid-cols-4 {
                grid-template-columns: repeat(2, 1fr);
            }

            .flex {
                flex-direction: column;
            }

            .space-x-4>*+* {
                margin-left: 0;
                margin-top: 0.5rem;
            }
        }

        /* Print styles */
        @media print {
            .no-print {
                display: none !important;
            }

            .data-table {
                border: 1px solid #000;
            }

            .data-table th,
            .data-table td {
                border: 1px solid #000;
                padding: 0.5rem;
            }

            body {
                font-size: 12px;
            }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Focus styles for accessibility */
        .form-input:focus,
        .btn:focus,
        .tab-button:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Error states */
        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        /* Success states */
        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .success-message {
            color: #10b981;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl md:text-3xl font-bold flex items-center">
                    <i class="fas fa-file-contract ml-3"></i>
                    نظام إدارة العقود والطبليات
                </h1>
                <div class="text-sm opacity-90">
                    الجمعيات التعاونية
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8">
        <!-- Navigation Tabs -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="flex border-b">
                <button id="contractsTab"
                    class="tab-button active px-6 py-4 font-semibold border-b-2 border-blue-500 text-blue-600">
                    <i class="fas fa-list ml-2"></i>
                    عرض العقود
                </button>
                <button id="addContractTab"
                    class="tab-button px-6 py-4 font-semibold text-gray-600 hover:text-blue-600">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة عقد جديد
                </button>
                <button id="suppliersTab" class="tab-button px-6 py-4 font-semibold text-gray-600 hover:text-blue-600">
                    <i class="fas fa-users ml-2"></i>
                    إدارة الموردين
                </button>
            </div>
        </div>

        <!-- Contracts List Tab -->
        <div id="contractsContent" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">قائمة العقود</h2>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <input type="text" id="searchInput" placeholder="البحث في العقود..."
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button onclick="exportToExcel()"
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-file-excel ml-2"></i>
                            تصدير Excel
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-blue-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي العقود</p>
                                <p id="totalContracts" class="text-xl font-bold text-blue-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <i class="fas fa-cube text-green-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي الطبليات</p>
                                <p id="totalTables" class="text-xl font-bold text-green-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <div class="flex items-center">
                            <i class="fas fa-eye text-yellow-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي العيون</p>
                                <p id="totalEyes" class="text-xl font-bold text-yellow-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <div class="flex items-center">
                            <i class="fas fa-money-bill-wave text-purple-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي القيمة</p>
                                <p id="totalValue" class="text-xl font-bold text-purple-600">0 د.ك</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contracts Table -->
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">رقم العقد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">اسم المورد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">عدد الطبليات</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">عدد العيون</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">قيمة إيجار العين
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">إجمالي القيمة</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">نسبة الخصم</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">القيمة بعد الخصم
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">تاريخ البداية</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">تاريخ النهاية</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="contractsTableBody">
                            <!-- Contracts will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Contract Tab -->
        <div id="addContractContent" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6">إضافة عقد جديد</h2>

                <form id="contractForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Contract Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم العقد *</label>
                            <input type="text" id="contractNumber" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل رقم العقد">
                        </div>

                        <!-- Supplier Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد *</label>
                            <select id="supplierName" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر المورد</option>
                            </select>
                        </div>

                        <!-- Number of Tables -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عدد الطبليات *</label>
                            <input type="number" id="numberOfTables" required min="1"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل عدد الطبليات" onchange="calculateTotals()">
                        </div>

                        <!-- Number of Eyes (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عدد العيون</label>
                            <input type="number" id="numberOfEyes" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Rent per Eye -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">قيمة إيجار العين (د.ك)</label>
                            <input type="number" id="rentPerEye" value="60" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100">
                        </div>

                        <!-- Total Rent (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي قيمة الإيجار
                                (د.ك)</label>
                            <input type="number" id="totalRent" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Discount Percentage -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نسبة الخصم (%)</label>
                            <input type="number" id="discountPercentage" min="0" max="100" step="0.01"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل نسبة الخصم" onchange="calculateTotals()">
                        </div>

                        <!-- Final Amount (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">القيمة النهائية بعد الخصم
                                (د.ك)</label>
                            <input type="number" id="finalAmount" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Start Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ بداية العقد *</label>
                            <input type="date" id="startDate" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <!-- End Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ نهاية العقد *</label>
                            <input type="date" id="endDate" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                        <textarea id="notes" rows="3"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="أدخل أي ملاحظات إضافية"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4 space-x-reverse">
                        <button type="button" onclick="resetForm()"
                            class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            إلغاء
                        </button>
                        <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-save ml-2"></i>
                            حفظ العقد
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Suppliers Management Tab -->
        <div id="suppliersContent" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">إدارة الموردين</h2>
                    <div class="flex space-x-4 space-x-reverse">
                        <button onclick="showAddSupplierModal()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة مورد جديد
                        </button>
                        <button onclick="loadSpiceSuppliers()"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-download ml-2"></i>
                            تحميل موردين البهارات
                        </button>
                        <button onclick="exportSuppliersToJSON()"
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-file-export ml-2"></i>
                            تصدير الموردين
                        </button>
                        <button onclick="importSuppliersFromJSON()"
                            class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-file-import ml-2"></i>
                            استيراد موردين
                        </button>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">اسم المورد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">رقم الهاتف</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">البريد الإلكتروني
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">العنوان</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliersTableBody">
                            <!-- Suppliers will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div id="supplierModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">إضافة مورد جديد</h3>
                <button onclick="hideAddSupplierModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="supplierForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد *</label>
                    <input type="text" id="supplierNameInput" required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input type="tel" id="supplierPhone"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="supplierEmail"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea id="supplierAddress" rows="2"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>

                <div class="flex justify-end space-x-4 space-x-reverse pt-4">
                    <button type="button" onclick="hideAddSupplierModal()"
                        class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let contracts = [];
        let suppliers = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function () {
            loadData();
            setupEventListeners();
            updateStatistics();
            populateSupplierDropdown();
            displayContracts();
            displaySuppliers();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Tab switching
            document.getElementById('contractsTab').addEventListener('click', () => switchTab('contracts'));
            document.getElementById('addContractTab').addEventListener('click', () => switchTab('addContract'));
            document.getElementById('suppliersTab').addEventListener('click', () => switchTab('suppliers'));

            // Form submissions
            document.getElementById('contractForm').addEventListener('submit', handleContractSubmit);
            document.getElementById('supplierForm').addEventListener('submit', handleSupplierSubmit);

            // Search functionality
            document.getElementById('searchInput').addEventListener('input', filterContracts);
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-blue-500', 'text-blue-600');
                button.classList.add('text-gray-600');
            });

            // Show selected tab content
            document.getElementById(tabName + 'Content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + 'Tab');
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('text-gray-600');
        }

        // Load data from localStorage
        function loadData() {
            const savedContracts = localStorage.getItem('contracts');
            const savedSuppliers = localStorage.getItem('suppliers');

            if (savedContracts) {
                contracts = JSON.parse(savedContracts);
            }

            if (savedSuppliers) {
                suppliers = JSON.parse(savedSuppliers);
            } else {
                // Add default spice suppliers for Kuwait
                suppliers = [
                    { id: 1, name: 'مورد البهارات الذهبية', phone: '99887766', email: '<EMAIL>', address: 'سوق المباركية - الكويت' },
                    { id: 2, name: 'شركة بهارات الخليج', phone: '99776655', email: '<EMAIL>', address: 'السوق المركزي - حولي' },
                    { id: 3, name: 'مؤسسة التوابل الكويتية', phone: '99665544', email: '<EMAIL>', address: 'سوق الجمعة - الفروانية' },
                    { id: 4, name: 'بهارات الشرق الأوسط', phone: '99554433', email: '<EMAIL>', address: 'سوق الأحمدي - الأحمدي' },
                    { id: 5, name: 'مورد التوابل الهندية', phone: '99443322', email: '<EMAIL>', address: 'منطقة الجهراء - الجهراء' },
                    { id: 6, name: 'شركة البهارات العربية', phone: '99332211', email: '<EMAIL>', address: 'سوق الصفاة - الكويت' },
                    { id: 7, name: 'مؤسسة بهارات الأصالة', phone: '99221100', email: '<EMAIL>', address: 'منطقة مبارك الكبير - مبارك الكبير' },
                    { id: 8, name: 'تجارة التوابل الدولية', phone: '99110099', email: '<EMAIL>', address: 'منطقة العاصمة - الكويت' },
                    { id: 9, name: 'بهارات السلطان', phone: '99009988', email: '<EMAIL>', address: 'سوق الخضار - حولي' },
                    { id: 10, name: 'مورد البهارات الطبيعية', phone: '98998877', email: '<EMAIL>', address: 'منطقة الفروانية - الفروانية' },
                    { id: 11, name: 'شركة التوابل المختارة', phone: '98887766', email: '<EMAIL>', address: 'سوق الجمعة - الأحمدي' },
                    { id: 12, name: 'بهارات الجودة العالية', phone: '98776655', email: '<EMAIL>', address: 'منطقة الجهراء - الجهراء' },
                    { id: 13, name: 'مؤسسة البهارات الملكية', phone: '98665544', email: '<EMAIL>', address: 'سوق المباركية - الكويت' },
                    { id: 14, name: 'تجارة التوابل الفاخرة', phone: '98554433', email: '<EMAIL>', address: 'منطقة مبارك الكبير - مبارك الكبير' },
                    { id: 15, name: 'بهارات الخليج الممتازة', phone: '98443322', email: '<EMAIL>', address: 'السوق المركزي - حولي' }
                ];
                saveData();
            }
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('contracts', JSON.stringify(contracts));
            localStorage.setItem('suppliers', JSON.stringify(suppliers));
        }

        // Calculate totals when tables or discount changes
        function calculateTotals() {
            const numberOfTables = parseInt(document.getElementById('numberOfTables').value) || 0;
            const discountPercentage = parseFloat(document.getElementById('discountPercentage').value) || 0;
            const rentPerEye = 60; // Fixed value

            // Calculate number of eyes (4 eyes per table)
            const numberOfEyes = numberOfTables * 4;
            document.getElementById('numberOfEyes').value = numberOfEyes;

            // Calculate total rent
            const totalRent = numberOfEyes * rentPerEye;
            document.getElementById('totalRent').value = totalRent;

            // Calculate final amount after discount
            const discountAmount = (totalRent * discountPercentage) / 100;
            const finalAmount = totalRent - discountAmount;
            document.getElementById('finalAmount').value = finalAmount.toFixed(2);
        }

        // Handle contract form submission
        function handleContractSubmit(e) {
            e.preventDefault();

            const formData = {
                id: Date.now(),
                contractNumber: document.getElementById('contractNumber').value,
                supplierName: document.getElementById('supplierName').value,
                numberOfTables: parseInt(document.getElementById('numberOfTables').value),
                numberOfEyes: parseInt(document.getElementById('numberOfEyes').value),
                rentPerEye: 60,
                totalRent: parseFloat(document.getElementById('totalRent').value),
                discountPercentage: parseFloat(document.getElementById('discountPercentage').value) || 0,
                finalAmount: parseFloat(document.getElementById('finalAmount').value),
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                notes: document.getElementById('notes').value
            };

            // Validate contract number uniqueness
            if (contracts.some(contract => contract.contractNumber === formData.contractNumber)) {
                alert('رقم العقد موجود مسبقاً. يرجى استخدام رقم مختلف.');
                return;
            }

            contracts.push(formData);
            saveData();
            displayContracts();
            updateStatistics();
            resetForm();

            alert('تم حفظ العقد بنجاح!');
            switchTab('contracts');
        }

        // Handle supplier form submission
        function handleSupplierSubmit(e) {
            e.preventDefault();

            const formData = {
                id: Date.now(),
                name: document.getElementById('supplierNameInput').value,
                phone: document.getElementById('supplierPhone').value,
                email: document.getElementById('supplierEmail').value,
                address: document.getElementById('supplierAddress').value
            };

            suppliers.push(formData);
            saveData();
            displaySuppliers();
            populateSupplierDropdown();
            hideAddSupplierModal();

            alert('تم إضافة المورد بنجاح!');
        }

        // Reset contract form
        function resetForm() {
            document.getElementById('contractForm').reset();
            document.getElementById('numberOfEyes').value = '';
            document.getElementById('totalRent').value = '';
            document.getElementById('finalAmount').value = '';
            document.getElementById('rentPerEye').value = 60;
        }

        // Display contracts in table
        function displayContracts() {
            const tbody = document.getElementById('contractsTableBody');
            tbody.innerHTML = '';

            contracts.forEach(contract => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-3">${contract.contractNumber}</td>
                    <td class="border border-gray-300 px-4 py-3">${contract.supplierName}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${contract.numberOfTables}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${contract.numberOfEyes}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${contract.rentPerEye} د.ك</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${contract.totalRent} د.ك</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${contract.discountPercentage}%</td>
                    <td class="border border-gray-300 px-4 py-3 text-center font-semibold text-green-600">${contract.finalAmount} د.ك</td>
                    <td class="border border-gray-300 px-4 py-3">${formatDate(contract.startDate)}</td>
                    <td class="border border-gray-300 px-4 py-3">${formatDate(contract.endDate)}</td>
                    <td class="border border-gray-300 px-4 py-3">
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="editContract(${contract.id})" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteContract(${contract.id})" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button onclick="viewContract(${contract.id})" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Display suppliers in table
        function displaySuppliers() {
            const tbody = document.getElementById('suppliersTableBody');
            tbody.innerHTML = '';

            suppliers.forEach(supplier => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-3">${supplier.name}</td>
                    <td class="border border-gray-300 px-4 py-3">${supplier.phone || '-'}</td>
                    <td class="border border-gray-300 px-4 py-3">${supplier.email || '-'}</td>
                    <td class="border border-gray-300 px-4 py-3">${supplier.address || '-'}</td>
                    <td class="border border-gray-300 px-4 py-3">
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="editSupplier(${supplier.id})" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Populate supplier dropdown
        function populateSupplierDropdown() {
            const select = document.getElementById('supplierName');
            select.innerHTML = '<option value="">اختر المورد</option>';

            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.name;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
        }

        // Update statistics
        function updateStatistics() {
            const totalContracts = contracts.length;
            const totalTables = contracts.reduce((sum, contract) => sum + contract.numberOfTables, 0);
            const totalEyes = contracts.reduce((sum, contract) => sum + contract.numberOfEyes, 0);
            const totalValue = contracts.reduce((sum, contract) => sum + contract.finalAmount, 0);

            document.getElementById('totalContracts').textContent = totalContracts;
            document.getElementById('totalTables').textContent = totalTables;
            document.getElementById('totalEyes').textContent = totalEyes;
            document.getElementById('totalValue').textContent = totalValue.toFixed(2) + ' د.ك';
        }

        // Filter contracts based on search input
        function filterContracts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#contractsTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Format date for display
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // Modal functions
        function showAddSupplierModal() {
            document.getElementById('supplierModal').classList.remove('hidden');
            document.getElementById('supplierModal').classList.add('flex');
        }

        function hideAddSupplierModal() {
            document.getElementById('supplierModal').classList.add('hidden');
            document.getElementById('supplierModal').classList.remove('flex');
            document.getElementById('supplierForm').reset();
        }

        // Contract actions
        function editContract(id) {
            const contract = contracts.find(c => c.id === id);
            if (contract) {
                // Populate form with contract data
                document.getElementById('contractNumber').value = contract.contractNumber;
                document.getElementById('supplierName').value = contract.supplierName;
                document.getElementById('numberOfTables').value = contract.numberOfTables;
                document.getElementById('discountPercentage').value = contract.discountPercentage;
                document.getElementById('startDate').value = contract.startDate;
                document.getElementById('endDate').value = contract.endDate;
                document.getElementById('notes').value = contract.notes;

                calculateTotals();

                // Remove the contract from array (will be re-added when form is submitted)
                contracts = contracts.filter(c => c.id !== id);
                saveData();

                switchTab('addContract');
            }
        }

        function deleteContract(id) {
            if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
                contracts = contracts.filter(c => c.id !== id);
                saveData();
                displayContracts();
                updateStatistics();
            }
        }

        function viewContract(id) {
            const contract = contracts.find(c => c.id === id);
            if (contract) {
                alert(`تفاصيل العقد:
رقم العقد: ${contract.contractNumber}
المورد: ${contract.supplierName}
عدد الطبليات: ${contract.numberOfTables}
عدد العيون: ${contract.numberOfEyes}
القيمة النهائية: ${contract.finalAmount} د.ك
تاريخ البداية: ${formatDate(contract.startDate)}
تاريخ النهاية: ${formatDate(contract.endDate)}
${contract.notes ? 'ملاحظات: ' + contract.notes : ''}`);
            }
        }

        // Supplier actions
        function editSupplier(id) {
            const supplier = suppliers.find(s => s.id === id);
            if (supplier) {
                document.getElementById('supplierNameInput').value = supplier.name;
                document.getElementById('supplierPhone').value = supplier.phone;
                document.getElementById('supplierEmail').value = supplier.email;
                document.getElementById('supplierAddress').value = supplier.address;

                // Remove the supplier from array (will be re-added when form is submitted)
                suppliers = suppliers.filter(s => s.id !== id);
                saveData();

                showAddSupplierModal();
            }
        }

        function deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                suppliers = suppliers.filter(s => s.id !== id);
                saveData();
                displaySuppliers();
                populateSupplierDropdown();
            }
        }

        // Export to Excel functionality
        function exportToExcel() {
            if (contracts.length === 0) {
                alert('لا توجد عقود للتصدير');
                return;
            }

            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "رقم العقد,اسم المورد,عدد الطبليات,عدد العيون,قيمة إيجار العين,إجمالي القيمة,نسبة الخصم,القيمة بعد الخصم,تاريخ البداية,تاريخ النهاية,ملاحظات\n";

            contracts.forEach(contract => {
                const row = [
                    contract.contractNumber,
                    contract.supplierName,
                    contract.numberOfTables,
                    contract.numberOfEyes,
                    contract.rentPerEye,
                    contract.totalRent,
                    contract.discountPercentage + '%',
                    contract.finalAmount,
                    contract.startDate,
                    contract.endDate,
                    contract.notes || ''
                ].join(',');
                csvContent += row + "\n";
            });

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "contracts_" + new Date().toISOString().split('T')[0] + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Load spice suppliers from predefined list
        function loadSpiceSuppliers() {
            // Check if there are imported suppliers from the spice suppliers list page
            const importedSuppliers = localStorage.getItem('importedSpiceSuppliers');

            if (importedSuppliers) {
                const parsedSuppliers = JSON.parse(importedSuppliers);
                let addedCount = 0;

                parsedSuppliers.forEach(supplier => {
                    const exists = suppliers.some(s => s.name === supplier.name);
                    if (!exists) {
                        suppliers.push(supplier);
                        addedCount++;
                    }
                });

                // Clear the imported data after use
                localStorage.removeItem('importedSpiceSuppliers');

                if (addedCount > 0) {
                    saveData();
                    displaySuppliers();
                    populateSupplierDropdown();
                    alert(`تم تحميل ${addedCount} مورد بهارات من القائمة المحسنة!`);
                } else {
                    alert('جميع موردين البهارات موجودين مسبقاً.');
                }
            } else if (confirm('هل تريد تحميل قائمة موردين البهارات الافتراضية؟ سيتم إضافة الموردين الجدد فقط.')) {
                const spiceSuppliers = [
                    { id: Date.now() + 1, name: 'مورد البهارات الذهبية', phone: '99887766', email: '<EMAIL>', address: 'سوق المباركية - الكويت' },
                    { id: Date.now() + 2, name: 'شركة بهارات الخليج', phone: '99776655', email: '<EMAIL>', address: 'السوق المركزي - حولي' },
                    { id: Date.now() + 3, name: 'مؤسسة التوابل الكويتية', phone: '99665544', email: '<EMAIL>', address: 'سوق الجمعة - الفروانية' },
                    { id: Date.now() + 4, name: 'بهارات الشرق الأوسط', phone: '99554433', email: '<EMAIL>', address: 'سوق الأحمدي - الأحمدي' },
                    { id: Date.now() + 5, name: 'مورد التوابل الهندية', phone: '99443322', email: '<EMAIL>', address: 'منطقة الجهراء - الجهراء' },
                    { id: Date.now() + 6, name: 'شركة البهارات العربية', phone: '99332211', email: '<EMAIL>', address: 'سوق الصفاة - الكويت' },
                    { id: Date.now() + 7, name: 'مؤسسة بهارات الأصالة', phone: '99221100', email: '<EMAIL>', address: 'منطقة مبارك الكبير - مبارك الكبير' },
                    { id: Date.now() + 8, name: 'تجارة التوابل الدولية', phone: '99110099', email: '<EMAIL>', address: 'منطقة العاصمة - الكويت' },
                    { id: Date.now() + 9, name: 'بهارات السلطان', phone: '99009988', email: '<EMAIL>', address: 'سوق الخضار - حولي' },
                    { id: Date.now() + 10, name: 'مورد البهارات الطبيعية', phone: '98998877', email: '<EMAIL>', address: 'منطقة الفروانية - الفروانية' },
                    { id: Date.now() + 11, name: 'شركة التوابل المختارة', phone: '98887766', email: '<EMAIL>', address: 'سوق الجمعة - الأحمدي' },
                    { id: Date.now() + 12, name: 'بهارات الجودة العالية', phone: '98776655', email: '<EMAIL>', address: 'منطقة الجهراء - الجهراء' },
                    { id: Date.now() + 13, name: 'مؤسسة البهارات الملكية', phone: '98665544', email: '<EMAIL>', address: 'سوق المباركية - الكويت' },
                    { id: Date.now() + 14, name: 'تجارة التوابل الفاخرة', phone: '98554433', email: '<EMAIL>', address: 'منطقة مبارك الكبير - مبارك الكبير' },
                    { id: Date.now() + 15, name: 'بهارات الخليج الممتازة', phone: '98443322', email: '<EMAIL>', address: 'السوق المركزي - حولي' }
                ];

                let addedCount = 0;
                spiceSuppliers.forEach(newSupplier => {
                    // Check if supplier already exists by name
                    const exists = suppliers.some(supplier => supplier.name === newSupplier.name);
                    if (!exists) {
                        suppliers.push(newSupplier);
                        addedCount++;
                    }
                });

                if (addedCount > 0) {
                    saveData();
                    displaySuppliers();
                    populateSupplierDropdown();
                    alert(`تم إضافة ${addedCount} مورد جديد بنجاح!`);
                } else {
                    alert('جميع موردين البهارات موجودين مسبقاً.');
                }
            }
        }

        // Export suppliers to JSON
        function exportSuppliersToJSON() {
            if (suppliers.length === 0) {
                alert('لا توجد موردين للتصدير');
                return;
            }

            const exportData = {
                suppliers: suppliers,
                metadata: {
                    total_suppliers: suppliers.length,
                    export_date: new Date().toISOString().split('T')[0],
                    exported_by: "نظام إدارة العقود والموردين"
                }
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `suppliers_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            alert('تم تصدير قائمة الموردين بنجاح!');
        }

        // Import suppliers from JSON file
        function importSuppliersFromJSON() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function (event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            let importedSuppliers = [];

                            // Handle different JSON formats
                            if (data.suppliers) {
                                importedSuppliers = data.suppliers;
                            } else if (data.موردين_البهارات) {
                                importedSuppliers = data.موردين_البهارات;
                            } else if (Array.isArray(data)) {
                                importedSuppliers = data;
                            }

                            let addedCount = 0;
                            importedSuppliers.forEach(newSupplier => {
                                // Check if supplier already exists by name
                                const exists = suppliers.some(supplier => supplier.name === newSupplier.name);
                                if (!exists) {
                                    // Ensure unique ID
                                    newSupplier.id = Date.now() + Math.random();
                                    suppliers.push(newSupplier);
                                    addedCount++;
                                }
                            });

                            if (addedCount > 0) {
                                saveData();
                                displaySuppliers();
                                populateSupplierDropdown();
                                alert(`تم استيراد ${addedCount} مورد جديد بنجاح!`);
                            } else {
                                alert('جميع الموردين موجودين مسبقاً.');
                            }
                        } catch (error) {
                            alert('خطأ في قراءة الملف. تأكد من أن الملف بصيغة JSON صحيحة.');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
    </script>
</body>

</html>