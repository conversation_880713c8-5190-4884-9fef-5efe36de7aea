<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقود والطبليات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['<PERSON>jawal', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Aria<PERSON>, sans-serif;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hover-scale {
            transition: transform 0.2s ease;
        }

        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl md:text-3xl font-bold flex items-center">
                    <i class="fas fa-file-contract ml-3"></i>
                    نظام إدارة العقود والطبليات
                </h1>
                <div class="text-sm opacity-90">
                    الجمعيات التعاونية
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8">
        <!-- Navigation Tabs -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="flex border-b">
                <button id="contractsTab"
                    class="tab-button active px-6 py-4 font-semibold border-b-2 border-blue-500 text-blue-600">
                    <i class="fas fa-list ml-2"></i>
                    عرض العقود
                </button>
                <button id="addContractTab"
                    class="tab-button px-6 py-4 font-semibold text-gray-600 hover:text-blue-600">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة عقد جديد
                </button>
                <button id="suppliersTab" class="tab-button px-6 py-4 font-semibold text-gray-600 hover:text-blue-600">
                    <i class="fas fa-users ml-2"></i>
                    إدارة الموردين
                </button>
            </div>
        </div>

        <!-- Contracts List Tab -->
        <div id="contractsContent" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">قائمة العقود</h2>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <input type="text" id="searchInput" placeholder="البحث في العقود..."
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button onclick="exportToExcel()"
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-file-excel ml-2"></i>
                            تصدير Excel
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-blue-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي العقود</p>
                                <p id="totalContracts" class="text-xl font-bold text-blue-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <i class="fas fa-cube text-green-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي الطبليات</p>
                                <p id="totalTables" class="text-xl font-bold text-green-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <div class="flex items-center">
                            <i class="fas fa-eye text-yellow-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي العيون</p>
                                <p id="totalEyes" class="text-xl font-bold text-yellow-600">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                        <div class="flex items-center">
                            <i class="fas fa-money-bill-wave text-purple-500 text-2xl ml-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">إجمالي القيمة</p>
                                <p id="totalValue" class="text-xl font-bold text-purple-600">0 د.ك</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contracts Table -->
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">رقم العقد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">اسم المورد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">عدد الطبليات</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">عدد العيون</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">قيمة إيجار العين
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">إجمالي القيمة</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">نسبة الخصم</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">القيمة بعد الخصم
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">تاريخ البداية</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">تاريخ النهاية</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="contractsTableBody">
                            <!-- Contracts will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Contract Tab -->
        <div id="addContractContent" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6">إضافة عقد جديد</h2>

                <form id="contractForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Contract Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم العقد *</label>
                            <input type="text" id="contractNumber" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل رقم العقد">
                        </div>

                        <!-- Supplier Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد *</label>
                            <select id="supplierName" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر المورد</option>
                            </select>
                        </div>

                        <!-- Number of Tables -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عدد الطبليات *</label>
                            <input type="number" id="numberOfTables" required min="1"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل عدد الطبليات" onchange="calculateTotals()">
                        </div>

                        <!-- Number of Eyes (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عدد العيون</label>
                            <input type="number" id="numberOfEyes" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Rent per Eye -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">قيمة إيجار العين (د.ك)</label>
                            <input type="number" id="rentPerEye" value="60" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100">
                        </div>

                        <!-- Total Rent (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي قيمة الإيجار
                                (د.ك)</label>
                            <input type="number" id="totalRent" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Discount Percentage -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نسبة الخصم (%)</label>
                            <input type="number" id="discountPercentage" min="0" max="100" step="0.01"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل نسبة الخصم" onchange="calculateTotals()">
                        </div>

                        <!-- Final Amount (Auto-calculated) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">القيمة النهائية بعد الخصم
                                (د.ك)</label>
                            <input type="number" id="finalAmount" readonly
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <!-- Start Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ بداية العقد *</label>
                            <input type="date" id="startDate" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <!-- End Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ نهاية العقد *</label>
                            <input type="date" id="endDate" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                        <textarea id="notes" rows="3"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="أدخل أي ملاحظات إضافية"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4 space-x-reverse">
                        <button type="button" onclick="resetForm()"
                            class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            إلغاء
                        </button>
                        <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-save ml-2"></i>
                            حفظ العقد
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Suppliers Management Tab -->
        <div id="suppliersContent" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">إدارة الموردين</h2>
                    <button onclick="showAddSupplierModal()"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة مورد جديد
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">اسم المورد</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">رقم الهاتف</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">البريد الإلكتروني
                                </th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">العنوان</th>
                                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliersTableBody">
                            <!-- Suppliers will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div id="supplierModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">إضافة مورد جديد</h3>
                <button onclick="hideAddSupplierModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="supplierForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المورد *</label>
                    <input type="text" id="supplierNameInput" required
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input type="tel" id="supplierPhone"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="supplierEmail"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea id="supplierAddress" rows="2"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>

                <div class="flex justify-end space-x-4 space-x-reverse pt-4">
                    <button type="button" onclick="hideAddSupplierModal()"
                        class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>