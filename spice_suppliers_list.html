<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة موردي البهارات - محسنة</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts for better Arabic support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Applying Cairo font to the body */
        body {
            font-family: 'Cairo', sans-serif;
        }

        /* Custom styles for better readability and aesthetics */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-weight: 600;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-suspended {
            background-color: #fee2e2;
            /* red-100 */
            color: #b91c1c;
            /* red-700 */
        }

        .status-contract {
            background-color: #dcfce7;
            /* green-100 */
            color: #166534;
            /* green-700 */
        }

        .status-percentage {
            background-color: #dbeafe;
            /* blue-100 */
            color: #1d4ed8;
            /* blue-700 */
        }

        .status-not-supplied {
            color: #6b7280;
            /* gray-500 */
            font-style: italic;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stats-card-green {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
        }

        .stats-card-red {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        }

        .stats-card-blue {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        }

        .stats-card-gray {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .bg-gray-100 {
                background: white !important;
            }
        }
    </style>
</head>

<body class="bg-gray-100">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <!-- Header -->
        <div class="bg-white shadow-lg rounded-2xl overflow-hidden mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-800">قائمة موردي البهارات</h1>
                    <div class="flex gap-2 no-print">
                        <button onclick="exportToExcel()"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-file-excel mr-2"></i>
                            تصدير Excel
                        </button>
                        <button onclick="printTable()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-print mr-2"></i>
                            طباعة
                        </button>
                        <button onclick="importToMainSystem()"
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-upload mr-2"></i>
                            إضافة للنظام الرئيسي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6 no-print">
            <div class="stats-card rounded-xl p-4 text-center">
                <div class="text-2xl font-bold" id="total-suppliers">0</div>
                <div class="text-sm opacity-90">إجمالي الموردين</div>
            </div>
            <div class="stats-card-green rounded-xl p-4 text-center text-white">
                <div class="text-2xl font-bold" id="contract-suppliers">0</div>
                <div class="text-sm opacity-90">موردين بعقد</div>
            </div>
            <div class="stats-card-blue rounded-xl p-4 text-center text-white">
                <div class="text-2xl font-bold" id="percentage-suppliers">0</div>
                <div class="text-sm opacity-90">موردين بنسبة</div>
            </div>
            <div class="stats-card-red rounded-xl p-4 text-center text-white">
                <div class="text-2xl font-bold" id="suspended-suppliers">0</div>
                <div class="text-sm opacity-90">موردين موقوفين</div>
            </div>
            <div class="stats-card-gray rounded-xl p-4 text-center text-white">
                <div class="text-2xl font-bold" id="not-supplied">0</div>
                <div class="text-sm opacity-90">لم يوردوا</div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white shadow-lg rounded-2xl overflow-hidden mb-6 no-print">
            <div class="p-6">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث في الموردين</label>
                        <input type="text" id="search-input" placeholder="ابحث بالاسم أو رقم المورد..."
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="sm:w-48">
                        <label class="block text-sm font-medium text-gray-700 mb-2">تصفية حسب الحالة</label>
                        <select id="status-filter"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">جميع الحالات</option>
                            <option value="عقد">موردين بعقد</option>
                            <option value="نسبة">موردين بنسبة</option>
                            <option value="موقوف">موردين موقوفين</option>
                            <option value="لم يورد">لم يوردوا</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Table -->
        <div class="bg-white shadow-lg rounded-2xl overflow-hidden">
            <!-- Table container with horizontal scroll on small screens -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                رقم المورد
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                اسم المورد
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                ملاحظات
                            </th>
                        </tr>
                    </thead>
                    <tbody id="suppliers-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Supplier data will be injected here by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div id="loading" class="text-center p-8 text-gray-500">
                جاري تحميل البيانات...
            </div>
            <div id="no-results" class="text-center p-8 text-gray-500 hidden">
                لا توجد نتائج مطابقة للبحث
            </div>
        </div>
    </div>

    <script>
        // The JSON data provided by the user
        const jsonData = {
            "suppliers": [
                { "supplier_number": "٠٦٢٧", "supplier_name": "شركة توريدات الخليج للتجارة العامة والمقاولات", "notes": "لم يورد" },
                { "supplier_number": "١٠٠٥", "supplier_name": "شركة مريم برجس للتجارة العامة والمقاولات", "notes": "لم يورد" },
                { "supplier_number": "١٤٥٣", "supplier_name": "شركة انوار ابو حليفة للتجارة العامة والمقاولات", "notes": "نسبة % ١٠مجاني سوق لا يوجد عقد" },
                { "supplier_number": "۳۰۱۲", "supplier_name": "شركة البروتين الكويتية", "notes": "عقد د.ك ۱۰۰۰ +% ٥خصم سوق" },
                { "supplier_number": "۳۲۱۸", "supplier_name": "شركة مجموعة العماد المتحدة للتجارة العامة", "notes": "عقد % 0خصم من المحاسبة" },
                { "supplier_number": "۲۱۲۸", "supplier_name": "شركة اليسر فودز لاعمال تعبئة وتغليف المواد الغذائية", "notes": "موقوف" },
                { "supplier_number": "٢١٠٥", "supplier_name": "شركة عالم التوابل للمواد الغذائية", "notes": "نسبة % 10خصم لا يوجد عقد سوق" },
                { "supplier_number": "٢٠٨٥", "supplier_name": "شركة مطحنة ومحمصة بن السعد", "notes": "عقد د.ك ۹۰۰ سوق فقط" },
                { "supplier_number": "٢١٣٥", "supplier_name": "مصنع البيان لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۲۰۰ د.ك سوق + ۱۰ فروع" },
                { "supplier_number": "۲۲۱۲", "supplier_name": "شركه النهضه لتجاره الجملة والتجزئه", "notes": "عقد ۱۲۰۰ د.ك سوق فقط" },
                { "supplier_number": "٢٢٢٦", "supplier_name": "مؤسسه تبارك المتحده التجاريه", "notes": "لم يورد\nموقوف" },
                { "supplier_number": "٢١٧٦", "supplier_name": "بي كير للاستيراد والتصدير ووكيل بالعموله", "notes": "موقوف نسبة % 10سوق وفروع لا يوجد عقد" },
                { "supplier_number": "۲۲۲۹", "supplier_name": "شركة هيا للمواد الغذائيه", "notes": "موقوف نسبة % 10سوق لا يوجد عقد" },
                { "supplier_number": "٢٢٥٨", "supplier_name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
                { "supplier_number": "٢٢٣٤", "supplier_name": "شركة مطحنة ومحمصة بن الربيع", "notes": "نسبة % 15مجاني سوق لا يوجد عقد" },
                { "supplier_number": "٠٩٦٧", "supplier_name": "مصنع مجموعة النجمة الوطنية لتعبئة وتغليف المواد الغذائية", "notes": "عقد ۱۹۰۰ د.ك سوق والفرع %١٠" },
                { "supplier_number": "- ٢٢٥٨", "supplier_name": "شركة ديرة العز للتجارة العامة للمواد الغذائيه", "notes": "لم يورد" },
                { "supplier_number": "٣٠٤٢", "supplier_name": "شركة الزاحم وملهوترا", "notes": "عقد خصم محاسبة وبهارات % ١٠ع طلبات الشراء سوق وفروع" },
                { "supplier_number": "٢٢٦٩", "supplier_name": "سمارت مارکت", "notes": "نسبة % 15سوق وفروع لا يوجد عقد" }
            ]
        };

        let allSuppliers = [];
        let filteredSuppliers = [];

        document.addEventListener('DOMContentLoaded', function () {
            const tableBody = document.getElementById('suppliers-table-body');
            const loadingIndicator = document.getElementById('loading');
            const noResultsIndicator = document.getElementById('no-results');
            const searchInput = document.getElementById('search-input');
            const statusFilter = document.getElementById('status-filter');

            if (jsonData && jsonData.suppliers) {
                allSuppliers = jsonData.suppliers;
                filteredSuppliers = [...allSuppliers];

                // Clear loading indicator
                loadingIndicator.style.display = 'none';

                // Initial render
                renderTable();
                updateStatistics();

                // Add event listeners for search and filter
                searchInput.addEventListener('input', filterSuppliers);
                statusFilter.addEventListener('change', filterSuppliers);
            } else {
                loadingIndicator.innerText = 'فشل في تحميل البيانات.';
            }
        });

        function renderTable() {
            const tableBody = document.getElementById('suppliers-table-body');
            const noResultsIndicator = document.getElementById('no-results');

            tableBody.innerHTML = '';

            if (filteredSuppliers.length === 0) {
                noResultsIndicator.classList.remove('hidden');
                return;
            } else {
                noResultsIndicator.classList.add('hidden');
            }

            filteredSuppliers.forEach(supplier => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';

                // Function to create a table cell
                const createCell = (content, extraClasses = '') => {
                    const cell = document.createElement('td');
                    cell.className = `px-6 py-4 whitespace-nowrap text-sm text-gray-700 ${extraClasses}`;
                    cell.innerHTML = content;
                    return cell;
                };

                // Function to get status from notes
                const getStatus = (notes) => {
                    if (notes.includes('موقوف')) return 'موقوف';
                    if (notes.includes('عقد')) return 'عقد';
                    if (notes.includes('نسبة')) return 'نسبة';
                    if (notes.includes('لم يورد')) return 'لم يورد';
                    return 'أخرى';
                };

                // Function to format status with badges
                const formatStatus = (status) => {
                    switch (status) {
                        case 'موقوف':
                            return '<span class="status-badge status-suspended">موقوف</span>';
                        case 'عقد':
                            return '<span class="status-badge status-contract">عقد</span>';
                        case 'نسبة':
                            return '<span class="status-badge status-percentage">نسبة</span>';
                        case 'لم يورد':
                            return '<span class="status-not-supplied">لم يورد</span>';
                        default:
                            return '<span class="text-gray-500">أخرى</span>';
                    }
                };

                const status = getStatus(supplier.notes);

                const supplierNumberCell = createCell(supplier.supplier_number);
                const supplierNameCell = createCell(supplier.supplier_name, 'font-semibold');
                const statusCell = createCell(formatStatus(status));
                const notesCell = createCell(supplier.notes.replace(/\n/g, '<br>'));

                row.appendChild(supplierNumberCell);
                row.appendChild(supplierNameCell);
                row.appendChild(statusCell);
                row.appendChild(notesCell);

                tableBody.appendChild(row);
            });
        }

        function filterSuppliers() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;

            filteredSuppliers = allSuppliers.filter(supplier => {
                const matchesSearch = supplier.supplier_name.toLowerCase().includes(searchTerm) ||
                    supplier.supplier_number.includes(searchTerm) ||
                    supplier.notes.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || supplier.notes.includes(statusFilter);

                return matchesSearch && matchesStatus;
            });

            renderTable();
            updateStatistics();
        }

        function updateStatistics() {
            const total = allSuppliers.length;
            const contract = allSuppliers.filter(s => s.notes.includes('عقد')).length;
            const percentage = allSuppliers.filter(s => s.notes.includes('نسبة')).length;
            const suspended = allSuppliers.filter(s => s.notes.includes('موقوف')).length;
            const notSupplied = allSuppliers.filter(s => s.notes.includes('لم يورد')).length;

            document.getElementById('total-suppliers').textContent = total;
            document.getElementById('contract-suppliers').textContent = contract;
            document.getElementById('percentage-suppliers').textContent = percentage;
            document.getElementById('suspended-suppliers').textContent = suspended;
            document.getElementById('not-supplied').textContent = notSupplied;
        }

        function exportToExcel() {
            const headers = ['رقم المورد', 'اسم المورد', 'الحالة', 'ملاحظات'];
            let csvContent = headers.join(',') + '\n';

            filteredSuppliers.forEach(supplier => {
                const status = supplier.notes.includes('موقوف') ? 'موقوف' :
                    supplier.notes.includes('عقد') ? 'عقد' :
                        supplier.notes.includes('نسبة') ? 'نسبة' :
                            supplier.notes.includes('لم يورد') ? 'لم يورد' : 'أخرى';

                const row = [
                    supplier.supplier_number,
                    supplier.supplier_name,
                    status,
                    supplier.notes.replace(/\n/g, ' ')
                ].map(field => `"${field}"`).join(',');

                csvContent += row + '\n';
            });

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `موردي_البهارات_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function printTable() {
            window.print();
        }

        function importToMainSystem() {
            // Convert suppliers to the format expected by the main system
            const convertedSuppliers = allSuppliers.map((supplier, index) => ({
                id: Date.now() + index,
                name: supplier.supplier_name,
                phone: '99000000', // Default phone number
                email: `supplier${supplier.supplier_number}@kuwait.com`,
                address: 'الكويت' // Default address
            }));

            // Save to localStorage for the main system to pick up
            localStorage.setItem('importedSpiceSuppliers', JSON.stringify(convertedSuppliers));

            alert(`تم تحضير ${convertedSuppliers.length} مورد للإضافة إلى النظام الرئيسي. افتح النظام الرئيسي واضغط "تحميل موردين البهارات" لإضافتهم.`);
        }
    </script>

</body>

</html>