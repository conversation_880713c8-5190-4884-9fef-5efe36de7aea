<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حصر الطبليات بالسوق المركزي - مصنف</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts for better Arabic support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }

        .category-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
        }

        .beverages {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .food {
            background-color: #dcfce7;
            color: #166534;
        }

        .cosmetics {
            background-color: #f3e8ff;
            color: #7c3aed;
        }

        .others {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .pharmaceuticals {
            background-color: #fef3c7;
            color: #d97706;
        }

        .general-trade {
            background-color: #f0f9ff;
            color: #0369a1;
        }
    </style>
</head>

<body class="bg-gray-100">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-blue-100 text-xs">المرطبات والمشروبات</p>
                    <p class="text-xl font-bold" id="beverages-count">0</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-green-100 text-xs">المواد الغذائية</p>
                    <p class="text-xl font-bold" id="food-count">0</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-purple-100 text-xs">العطور والتجميل</p>
                    <p class="text-xl font-bold" id="cosmetics-count">0</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-yellow-100 text-xs">الأدوية والكيماويات</p>
                    <p class="text-xl font-bold" id="pharmaceuticals-count">0</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-cyan-100 text-xs">التجارة العامة</p>
                    <p class="text-xl font-bold" id="general-trade-count">0</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-4 text-white shadow-lg">
                <div class="text-center">
                    <p class="text-red-100 text-xs">أخرى</p>
                    <p class="text-xl font-bold" id="others-count">0</p>
                </div>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text" id="search-input" placeholder="ابحث عن اسم الشركة..."
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="sm:w-48">
                    <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-2">تصفية حسب
                        النشاط</label>
                    <select id="category-filter"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">جميع الأنشطة</option>
                        <option value="beverages">المرطبات والمشروبات</option>
                        <option value="food">المواد الغذائية</option>
                        <option value="cosmetics">العطور والتجميل</option>
                        <option value="pharmaceuticals">الأدوية والكيماويات</option>
                        <option value="general-trade">التجارة العامة</option>
                        <option value="others">أخرى</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="bg-white shadow-lg rounded-2xl overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 text-center">تقرير حصر الطبليات بالسوق المركزي
                </h1>
                <p class="text-center text-gray-600 mt-2">إجمالي الشركات: <span id="total-companies"
                        class="font-bold text-blue-600">43</span> | المعروضة: <span id="displayed-companies"
                        class="font-bold text-green-600">43</span></p>
            </div>

            <!-- Table container with horizontal scroll on small screens -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">م
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                اسم الشركة</th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                نوع النشاط</th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                المساحة المستغلة</th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-bold text-gray-500 uppercase tracking-wider">
                                المساحة المسجلة</th>
                        </tr>
                    </thead>
                    <tbody id="report-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Report data will be injected here by JavaScript -->
                    </tbody>
                </table>
            </div>
            <div id="loading" class="text-center p-8 text-gray-500">
                جاري تحميل البيانات...
            </div>
        </div>
    </div>

    <script>
        // Function to categorize companies based on their names
        function categorizeCompany(name) {
            const lowerName = name.toLowerCase();

            if (lowerName.includes('مرطبات') || lowerName.includes('مشروبات') ||
                lowerName.includes('العوجان') || lowerName.includes('المتحدة للمرطبات') ||
                lowerName.includes('الساير')) {
                return 'beverages';
            }

            if (lowerName.includes('غذائية') || lowerName.includes('الالبان') ||
                lowerName.includes('اليسرة') || lowerName.includes('الزراعة') ||
                lowerName.includes('الجمعيات')) {
                return 'food';
            }

            if (lowerName.includes('عطور') || lowerName.includes('تربل عود') ||
                lowerName.includes('الفايز')) {
                return 'cosmetics';
            }

            if (lowerName.includes('ادوية') || lowerName.includes('المنتصر') ||
                lowerName.includes('الكيماوية') || lowerName.includes('الصانع')) {
                return 'pharmaceuticals';
            }

            if (lowerName.includes('تجارة') || lowerName.includes('التجارية') ||
                lowerName.includes('الاصناف') || lowerName.includes('التسويق') ||
                lowerName.includes('ديستر بيوشن') || lowerName.includes('انترناشيونال')) {
                return 'general-trade';
            }

            return 'others';
        }

        // Function to get category display name
        function getCategoryDisplayName(category) {
            const categories = {
                'beverages': 'المرطبات والمشروبات',
                'food': 'المواد الغذائية',
                'cosmetics': 'العطور والتجميل',
                'pharmaceuticals': 'الأدوية والكيماويات',
                'general-trade': 'التجارة العامة',
                'others': 'أخرى'
            };
            return categories[category] || 'غير محدد';
        }

        // Data extracted from the user's DOCX file with categories
        const reportData = [
            { id: '1', name: 'شركة مبارك النصافي', utilized_space: '3 قاطع', contracted_space: '', category: 'others' },
            { id: '2', name: 'شركة شذر الخليج', utilized_space: '6 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '3', name: 'مؤسسة متعب سعد (عنود الكويت سابقا)', utilized_space: '1 قاطع', contracted_space: '', category: 'general-trade' },
            { id: '4', name: 'شركة الامانة الخليجية', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '5', name: 'شركة الوطنية', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '6', name: 'شركة المنصورية', utilized_space: '4 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '7', name: 'شركة الكويت للزراعة', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '8', name: 'اتحاد الجمعيات', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '9', name: 'شركة على عبدالوهاب', utilized_space: '36 طبلية', contracted_space: '', category: 'others' },
            { id: '10', name: 'شركة محمد ناصر الهاجري', utilized_space: '1/4.2 طبلية', contracted_space: '', category: 'others' },
            { id: '11', name: 'شركة الشرق الاوسط', utilized_space: '2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '12', name: 'شركة هوت باك العالمية', utilized_space: '1/1.2 طبلية', contracted_space: '', category: 'food' },
            { id: '13', name: 'شركة محمد السبيعي', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '14', name: 'شركة الدرة النادرة', utilized_space: '1/1.2 طبلية', contracted_space: '', category: 'others' },
            { id: '15', name: 'شركة رعد للتسويق', utilized_space: '2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '16', name: 'شركة البشاير المشتركة', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '17', name: 'شركة المخازن البيضاء', utilized_space: '1/2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '18', name: 'شركة البشتان الوطنية', utilized_space: '1/2 طبلية', contracted_space: '', category: 'food' },
            { id: '19', name: 'شركة صناعات الشرهان', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'others' },
            { id: '20', name: 'شركة الصانع للمنتجات الكيماوية', utilized_space: '1 طبلية', contracted_space: '', category: 'pharmaceuticals' },
            { id: '21', name: 'شركة المنتصر للادوية', utilized_space: '1/9.2 طبلية', contracted_space: '', category: 'pharmaceuticals' },
            { id: '22', name: 'شركة تربل عود للعطور', utilized_space: '1 طبلية', contracted_space: '', category: 'cosmetics' },
            { id: '23', name: 'شركة الجسر انترناشيونال', utilized_space: '1/2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '24', name: 'مؤسسة الفايز للعطور', utilized_space: '1/2 طبلية', contracted_space: '', category: 'cosmetics' },
            { id: '25', name: 'شركة اولاد جاسم الوزان', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '26', name: 'شركة اليسرة فرع الالبان', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '27', name: 'شركة مسعد المحدودة', utilized_space: '2 طبلية', contracted_space: '', category: 'others' },
            { id: '28', name: 'شركة سان ريمون', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '29', name: 'شركة العوجان للمرطبات', utilized_space: '3 طبلية', contracted_space: '', category: 'beverages' },
            { id: '30', name: 'شركة المتحدة للمرطبات', utilized_space: '1 طبلية', contracted_space: '', category: 'beverages' },
            { id: '31', name: 'شركة الساير للمرطبات', utilized_space: 'تواجد', contracted_space: '', category: 'beverages' },
            { id: '32', name: 'مؤسسة ناصر الجزيرة', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '33', name: 'شركة الاصناف التجارية', utilized_space: '9 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '34', name: 'شركة محمد عبدالرحمن البحر', utilized_space: '1/7.2 طبلية', contracted_space: '', category: 'others' },
            { id: '35', name: 'شركة الزاحم وملهوترا', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '36', name: 'شركة البشر المتحدة', utilized_space: '1/9.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '37', name: 'شركة الخليج للتجارة والتبريد', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '38', name: 'شركة الهدف التسويقية', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '39', name: 'شركة الهدى الكويتية', utilized_space: '1/8.2 طبلية', contracted_space: '', category: 'others' },
            { id: '40', name: 'ونرز العالمية', utilized_space: '1/2 طبلية', contracted_space: '', category: 'others' },
            { id: '41', name: 'شركة ام تي سي ديستر بيوشن (الصناعية سابقا)', utilized_space: '5 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '42', name: 'شركة المرطبات التجارية', utilized_space: '4 طبلية', contracted_space: '', category: 'beverages' },
            { id: '43', name: 'شرة المتحدة للمرطبات', utilized_space: '5 طبليات', contracted_space: '', category: 'beverages' }
        ];

        let filteredData = [...reportData];

        // Update statistics
        function updateStatistics() {
            const stats = {
                beverages: 0,
                food: 0,
                cosmetics: 0,
                pharmaceuticals: 0,
                'general-trade': 0,
                others: 0
            };

            filteredData.forEach(item => {
                stats[item.category]++;
            });

            document.getElementById('beverages-count').textContent = stats.beverages;
            document.getElementById('food-count').textContent = stats.food;
            document.getElementById('cosmetics-count').textContent = stats.cosmetics;
            document.getElementById('pharmaceuticals-count').textContent = stats.pharmaceuticals;
            document.getElementById('general-trade-count').textContent = stats['general-trade'];
            document.getElementById('others-count').textContent = stats.others;
            document.getElementById('displayed-companies').textContent = filteredData.length;
        }

        // Filter and search functionality
        function filterData() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;

            filteredData = reportData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) ||
                    item.utilized_space.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || item.category === categoryFilter;
                return matchesSearch && matchesCategory;
            });

            renderTable();
            updateStatistics();
        }

        // Render table
        function renderTable() {
            const tableBody = document.getElementById('report-table-body');
            const loadingIndicator = document.getElementById('loading');

            tableBody.innerHTML = '';

            if (filteredData.length === 0) {
                loadingIndicator.style.display = 'block';
                loadingIndicator.textContent = 'لا توجد نتائج مطابقة للبحث.';
                return;
            }

            loadingIndicator.style.display = 'none';

            filteredData.forEach(item => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';

                const createCell = (content, isBold = false) => {
                    const cell = document.createElement('td');
                    cell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-700';
                    if (isBold) {
                        cell.classList.add('font-semibold');
                    }
                    cell.textContent = content || '—';
                    return cell;
                };

                const createCategoryCell = (category) => {
                    const cell = document.createElement('td');
                    cell.className = 'px-6 py-4 whitespace-nowrap text-sm';

                    const badge = document.createElement('span');
                    badge.className = `category-badge ${category}`;
                    badge.textContent = getCategoryDisplayName(category);

                    cell.appendChild(badge);
                    return cell;
                };

                row.appendChild(createCell(item.id));
                row.appendChild(createCell(item.name, true));
                row.appendChild(createCategoryCell(item.category));
                row.appendChild(createCell(item.utilized_space));
                row.appendChild(createCell(item.contracted_space));

                tableBody.appendChild(row);
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function () {
            renderTable();
            updateStatistics();

            // Add event listeners
            document.getElementById('search-input').addEventListener('input', filterData);
            document.getElementById('category-filter').addEventListener('change', filterData);
        });
    </script>

</body>

</html>