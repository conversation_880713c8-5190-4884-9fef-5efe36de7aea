<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المتكامل لإدارة العقود والموردين والطبليات</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&family=Tajawal:wght@400;500;700&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
        }

        .status-contract {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-percentage {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-suspended {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .status-not-supplied {
            color: #6b7280;
            font-style: italic;
        }

        .category-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
        }

        .beverages {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .food {
            background-color: #dcfce7;
            color: #166534;
        }

        .cosmetics {
            background-color: #f3e8ff;
            color: #7c3aed;
        }

        .others {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .pharmaceuticals {
            background-color: #fef3c7;
            color: #d97706;
        }

        .general-trade {
            background-color: #f0f9ff;
            color: #0369a1;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-button {
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .bg-gradient-to-r {
                background: #f8f9fa !important;
            }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>

<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <i class="fas fa-building text-3xl"></i>
                    <div>
                        <h1 class="text-2xl font-bold">النظام المتكامل للجمعيات التعاونية</h1>
                        <p class="text-blue-100">إدارة العقود والموردين والطبليات</p>
                    </div>
                </div>
                <div class="text-left">
                    <p class="text-sm text-blue-100">دولة الكويت</p>
                    <p class="text-xs text-blue-200">وزارة الشؤون الاجتماعية</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="bg-white shadow-md border-b border-gray-200 no-print">
        <div class="container mx-auto px-4">
            <div class="flex space-x-8 space-x-reverse overflow-x-auto">
                <button onclick="showTab('contracts')" id="contracts-tab"
                    class="tab-button px-6 py-4 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600 whitespace-nowrap active">
                    <i class="fas fa-file-contract ml-2"></i>
                    إدارة العقود
                </button>
                <button onclick="showTab('suppliers')" id="suppliers-tab"
                    class="tab-button px-6 py-4 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600 whitespace-nowrap">
                    <i class="fas fa-users ml-2"></i>
                    إدارة الموردين
                </button>
                <button onclick="showTab('spice-suppliers')" id="spice-suppliers-tab"
                    class="tab-button px-6 py-4 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600 whitespace-nowrap">
                    <i class="fas fa-pepper-hot ml-2"></i>
                    موردو البهارات
                </button>
                <button onclick="showTab('market-report')" id="market-report-tab"
                    class="tab-button px-6 py-4 text-sm font-medium text-gray-700 hover:text-blue-600 border-b-2 border-transparent hover:border-blue-600 whitespace-nowrap">
                    <i class="fas fa-chart-bar ml-2"></i>
                    تقرير الطبليات
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Contracts Management Tab -->
        <div id="contracts-content" class="tab-content active">
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-file-contract text-blue-600 ml-3"></i>
                    إدارة العقود
                </h2>

                <!-- Add Contract Form -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="space-y-4">
                        <div>
                            <label for="contract-number" class="block text-sm font-medium text-gray-700 mb-2">رقم
                                العقد</label>
                            <input type="text" id="contract-number"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل رقم العقد">
                        </div>

                        <div>
                            <label for="supplier-select" class="block text-sm font-medium text-gray-700 mb-2">اسم
                                المورد</label>
                            <select id="supplier-select"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر المورد</option>
                            </select>
                        </div>

                        <div>
                            <label for="tables-count" class="block text-sm font-medium text-gray-700 mb-2">عدد
                                الطبليات</label>
                            <input type="number" id="tables-count" min="1"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="أدخل عدد الطبليات">
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عدد العيون</label>
                            <input type="text" id="eyes-count" readonly
                                class="w-full px-4 py-2 bg-gray-100 border border-gray-300 rounded-lg"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">قيمة الإيجار الكلية
                                (د.ك)</label>
                            <input type="text" id="total-rent" readonly
                                class="w-full px-4 py-2 bg-gray-100 border border-gray-300 rounded-lg"
                                placeholder="يُحسب تلقائياً">
                        </div>

                        <button onclick="addContract()"
                            class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition duration-300 font-medium">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة العقد
                        </button>
                    </div>
                </div>

                <!-- Contracts Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    رقم العقد</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    اسم المورد</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    عدد الطبليات</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    عدد العيون</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    قيمة الإيجار</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider no-print">
                                    الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="contracts-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Contracts will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Suppliers Management Tab -->
        <div id="suppliers-content" class="tab-content">
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-users text-green-600 ml-3"></i>
                    إدارة الموردين
                </h2>

                <!-- Add Supplier Form -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="space-y-4">
                        <div>
                            <label for="supplier-name" class="block text-sm font-medium text-gray-700 mb-2">اسم
                                المورد</label>
                            <input type="text" id="supplier-name"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                placeholder="أدخل اسم المورد">
                        </div>

                        <div>
                            <label for="supplier-phone" class="block text-sm font-medium text-gray-700 mb-2">رقم
                                الهاتف</label>
                            <input type="tel" id="supplier-phone"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                placeholder="أدخل رقم الهاتف">
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label for="supplier-email" class="block text-sm font-medium text-gray-700 mb-2">البريد
                                الإلكتروني</label>
                            <input type="email" id="supplier-email"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                placeholder="أدخل البريد الإلكتروني">
                        </div>

                        <div>
                            <label for="supplier-address"
                                class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                            <input type="text" id="supplier-address"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                placeholder="أدخل العنوان">
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap gap-4 mb-6">
                    <button onclick="addSupplier()"
                        class="bg-gradient-to-r from-green-600 to-teal-600 text-white py-2 px-6 rounded-lg hover:from-green-700 hover:to-teal-700 transition duration-300">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة مورد
                    </button>

                    <button onclick="loadSpiceSuppliers()"
                        class="bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 px-6 rounded-lg hover:from-orange-600 hover:to-red-600 transition duration-300">
                        <i class="fas fa-pepper-hot ml-2"></i>
                        تحميل موردين البهارات
                    </button>

                    <button onclick="exportSuppliersToJSON()"
                        class="bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-6 rounded-lg hover:from-blue-600 hover:to-indigo-600 transition duration-300">
                        <i class="fas fa-download ml-2"></i>
                        تصدير الموردين
                    </button>

                    <label for="import-suppliers"
                        class="bg-gradient-to-r from-purple-500 to-pink-500 text-white py-2 px-6 rounded-lg hover:from-purple-600 hover:to-pink-600 transition duration-300 cursor-pointer">
                        <i class="fas fa-upload ml-2"></i>
                        استيراد موردين
                        <input type="file" id="import-suppliers" accept=".json" style="display: none;"
                            onchange="importSuppliers(event)">
                    </label>
                </div>

                <!-- Suppliers Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الاسم</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الهاتف</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    البريد الإلكتروني</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    العنوان</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider no-print">
                                    الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Suppliers will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Spice Suppliers Tab -->
        <div id="spice-suppliers-content" class="tab-content">
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-pepper-hot text-orange-600 ml-3"></i>
                    قائمة موردي البهارات المحسنة
                </h2>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div
                        class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-green-100 text-sm">عقد</p>
                            <p class="text-2xl font-bold" id="spice-contract-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-blue-100 text-sm">نسبة</p>
                            <p class="text-2xl font-bold" id="spice-percentage-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-red-100 text-sm">موقوف</p>
                            <p class="text-2xl font-bold" id="spice-suspended-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-gray-500 to-gray-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-gray-100 text-sm">لم يورد</p>
                            <p class="text-2xl font-bold" id="spice-not-supplied-count">0</p>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label for="spice-search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" id="spice-search" placeholder="ابحث عن اسم الشركة أو رقم المورد..."
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    </div>

                    <div>
                        <label for="spice-status-filter" class="block text-sm font-medium text-gray-700 mb-2">تصفية حسب
                            الحالة</label>
                        <select id="spice-status-filter"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            <option value="">جميع الحالات</option>
                            <option value="عقد">عقد</option>
                            <option value="نسبة">نسبة</option>
                            <option value="موقوف">موقوف</option>
                            <option value="لم يورد">لم يورد</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-4 mb-6 no-print">
                    <button onclick="exportSpiceSuppliersToExcel()"
                        class="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-6 rounded-lg hover:from-green-700 hover:to-emerald-700 transition duration-300">
                        <i class="fas fa-file-excel ml-2"></i>
                        تصدير Excel
                    </button>

                    <button onclick="printSpiceSuppliers()"
                        class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-2 px-6 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition duration-300">
                        <i class="fas fa-print ml-2"></i>
                        طباعة
                    </button>

                    <button onclick="importSpiceSuppliersToMain()"
                        class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-6 rounded-lg hover:from-purple-700 hover:to-pink-700 transition duration-300">
                        <i class="fas fa-arrow-right ml-2"></i>
                        إضافة للنظام الرئيسي
                    </button>
                </div>

                <!-- Spice Suppliers Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    رقم المورد</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    اسم الشركة</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الحالة</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody id="spice-suppliers-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Spice suppliers will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Market Report Tab -->
        <div id="market-report-content" class="tab-content">
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-chart-bar text-purple-600 ml-3"></i>
                    تقرير حصر الطبليات بالسوق المركزي
                </h2>

                <!-- Market Statistics Cards -->
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                    <div
                        class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-blue-100 text-xs">المرطبات والمشروبات</p>
                            <p class="text-xl font-bold" id="market-beverages-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-green-100 text-xs">المواد الغذائية</p>
                            <p class="text-xl font-bold" id="market-food-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-purple-100 text-xs">العطور والتجميل</p>
                            <p class="text-xl font-bold" id="market-cosmetics-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-yellow-100 text-xs">الأدوية والكيماويات</p>
                            <p class="text-xl font-bold" id="market-pharmaceuticals-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-cyan-100 text-xs">التجارة العامة</p>
                            <p class="text-xl font-bold" id="market-general-trade-count">0</p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-4 text-white shadow-lg card-hover">
                        <div class="text-center">
                            <p class="text-red-100 text-xs">أخرى</p>
                            <p class="text-xl font-bold" id="market-others-count">0</p>
                        </div>
                    </div>
                </div>

                <!-- Market Search and Filter -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label for="market-search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" id="market-search" placeholder="ابحث عن اسم الشركة..."
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div>
                        <label for="market-category-filter" class="block text-sm font-medium text-gray-700 mb-2">تصفية
                            حسب النشاط</label>
                        <select id="market-category-filter"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <option value="">جميع الأنشطة</option>
                            <option value="beverages">المرطبات والمشروبات</option>
                            <option value="food">المواد الغذائية</option>
                            <option value="cosmetics">العطور والتجميل</option>
                            <option value="pharmaceuticals">الأدوية والكيماويات</option>
                            <option value="general-trade">التجارة العامة</option>
                            <option value="others">أخرى</option>
                        </select>
                    </div>
                </div>

                <p class="text-center text-gray-600 mb-6">إجمالي الشركات: <span id="market-total-companies"
                        class="font-bold text-purple-600">43</span> | المعروضة: <span id="market-displayed-companies"
                        class="font-bold text-green-600">43</span></p>

                <!-- Market Report Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    م</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    اسم الشركة</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    نوع النشاط</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    المساحة المستغلة</th>
                                <th
                                    class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    المساحة المسجلة</th>
                            </tr>
                        </thead>
                        <tbody id="market-report-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Market report data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let contracts = JSON.parse(localStorage.getItem('contracts')) || [];
        let suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
        let currentTab = 'contracts';

        // Spice suppliers data
        const spiceSuppliersData = [
            { supplier_number: "٠٦٢٧", supplier_name: "شركة توريدات الخليج للتجارة العامة والمقاولات", notes: "لم يورد" },
            { supplier_number: "١٠٠٥", supplier_name: "شركة مريم برجس للتجارة العامة والمقاولات", notes: "لم يورد" },
            { supplier_number: "۳۰۱۲", supplier_name: "شركة البروتين الكويتية", notes: "عقد د.ك ۱۰۰۰ +% ٥خصم سوق" },
            { supplier_number: "٢١٠٥", supplier_name: "شركة عالم التوابل للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۲۸", supplier_name: "شركة اليسر فودز لاعمال تعبئة وتغليف المواد الغذائية", notes: "موقوف" },
            { supplier_number: "۲۱۳۰", supplier_name: "شركة الخليج الذهبي للمواد الغذائية", notes: "عقد د.ك ۱۰۰۰ +% ٥خصم سوق" },
            { supplier_number: "۲۱۳۱", supplier_name: "شركة الاختيار الاول للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳۲", supplier_name: "شركة الاختيار الثاني للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳۳", supplier_name: "شركة الاختيار الثالث للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳٤", supplier_name: "شركة الاختيار الرابع للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳٥", supplier_name: "شركة الاختيار الخامس للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳٦", supplier_name: "شركة الاختيار السادس للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳۷", supplier_name: "شركة الاختيار السابع للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳۸", supplier_name: "شركة الاختيار الثامن للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱۳۹", supplier_name: "شركة الاختيار التاسع للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱٤۰", supplier_name: "شركة الاختيار العاشر للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱٤۱", supplier_name: "شركة الاختيار الحادي عشر للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱٤۲", supplier_name: "شركة الاختيار الثاني عشر للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" },
            { supplier_number: "۲۱٤۳", supplier_name: "شركة الاختيار الثالث عشر للمواد الغذائية", notes: "نسبة % 10خصم لا يوجد عقد سوق" }
        ];

        // Market report data
        const marketReportData = [
            { id: '1', name: 'شركة مبارك النصافي', utilized_space: '3 قاطع', contracted_space: '', category: 'others' },
            { id: '2', name: 'شركة شذر الخليج', utilized_space: '6 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '3', name: 'مؤسسة متعب سعد (عنود الكويت سابقا)', utilized_space: '1 قاطع', contracted_space: '', category: 'general-trade' },
            { id: '4', name: 'شركة الامانة الخليجية', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '5', name: 'شركة الوطنية', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '6', name: 'شركة المنصورية', utilized_space: '4 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '7', name: 'شركة الكويت للزراعة', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '8', name: 'اتحاد الجمعيات', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '9', name: 'شركة على عبدالوهاب', utilized_space: '36 طبلية', contracted_space: '', category: 'others' },
            { id: '10', name: 'شركة محمد ناصر الهاجري', utilized_space: '1/4.2 طبلية', contracted_space: '', category: 'others' },
            { id: '11', name: 'شركة الشرق الاوسط', utilized_space: '2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '12', name: 'شركة هوت باك العالمية', utilized_space: '1/1.2 طبلية', contracted_space: '', category: 'food' },
            { id: '13', name: 'شركة محمد السبيعي', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '14', name: 'شركة الدرة النادرة', utilized_space: '1/1.2 طبلية', contracted_space: '', category: 'others' },
            { id: '15', name: 'شركة رعد للتسويق', utilized_space: '2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '16', name: 'شركة البشاير المشتركة', utilized_space: '1 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '17', name: 'شركة المخازن البيضاء', utilized_space: '1/2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '18', name: 'شركة البشتان الوطنية', utilized_space: '1/2 طبلية', contracted_space: '', category: 'food' },
            { id: '19', name: 'شركة صناعات الشرهان', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'others' },
            { id: '20', name: 'شركة الصانع للمنتجات الكيماوية', utilized_space: '1 طبلية', contracted_space: '', category: 'pharmaceuticals' },
            { id: '21', name: 'شركة المنتصر للادوية', utilized_space: '1/9.2 طبلية', contracted_space: '', category: 'pharmaceuticals' },
            { id: '22', name: 'شركة تربل عود للعطور', utilized_space: '1 طبلية', contracted_space: '', category: 'cosmetics' },
            { id: '23', name: 'شركة الجسر انترناشيونال', utilized_space: '1/2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '24', name: 'مؤسسة الفايز للعطور', utilized_space: '1/2 طبلية', contracted_space: '', category: 'cosmetics' },
            { id: '25', name: 'شركة اولاد جاسم الوزان', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '26', name: 'شركة اليسرة فرع الالبان', utilized_space: '1 طبلية', contracted_space: '', category: 'food' },
            { id: '27', name: 'شركة مسعد المحدودة', utilized_space: '2 طبلية', contracted_space: '', category: 'others' },
            { id: '28', name: 'شركة سان ريمون', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '29', name: 'شركة العوجان للمرطبات', utilized_space: '3 طبلية', contracted_space: '', category: 'beverages' },
            { id: '30', name: 'شركة المتحدة للمرطبات', utilized_space: '1 طبلية', contracted_space: '', category: 'beverages' },
            { id: '31', name: 'شركة الساير للمرطبات', utilized_space: 'تواجد', contracted_space: '', category: 'beverages' },
            { id: '32', name: 'مؤسسة ناصر الجزيرة', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '33', name: 'شركة الاصناف التجارية', utilized_space: '9 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '34', name: 'شركة محمد عبدالرحمن البحر', utilized_space: '1/7.2 طبلية', contracted_space: '', category: 'others' },
            { id: '35', name: 'شركة الزاحم وملهوترا', utilized_space: '1 طبلية', contracted_space: '', category: 'others' },
            { id: '36', name: 'شركة البشر المتحدة', utilized_space: '1/9.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '37', name: 'شركة الخليج للتجارة والتبريد', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '38', name: 'شركة الهدف التسويقية', utilized_space: '1/3.2 طبلية', contracted_space: '', category: 'general-trade' },
            { id: '39', name: 'شركة الهدى الكويتية', utilized_space: '1/8.2 طبلية', contracted_space: '', category: 'others' },
            { id: '40', name: 'ونرز العالمية', utilized_space: '1/2 طبلية', contracted_space: '', category: 'others' },
            { id: '41', name: 'شركة ام تي سي ديستر بيوشن (الصناعية سابقا)', utilized_space: '5 طبليات', contracted_space: '', category: 'general-trade' },
            { id: '42', name: 'شركة المرطبات التجارية', utilized_space: '4 طبلية', contracted_space: '', category: 'beverages' },
            { id: '43', name: 'شرة المتحدة للمرطبات', utilized_space: '5 طبليات', contracted_space: '', category: 'beverages' }
        ];

        let filteredSpiceSuppliers = [...spiceSuppliersData];
        let filteredMarketData = [...marketReportData];

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');

            currentTab = tabName;

            // Initialize tab-specific data
            if (tabName === 'spice-suppliers') {
                initializeSpiceSuppliers();
            } else if (tabName === 'market-report') {
                initializeMarketReport();
            }
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('contracts', JSON.stringify(contracts));
            localStorage.setItem('suppliers', JSON.stringify(suppliers));
        }

        // Contract management functions
        function calculateValues() {
            const tablesCount = parseInt(document.getElementById('tables-count').value) || 0;
            const eyesCount = tablesCount * 4;
            const totalRent = eyesCount * 60;

            document.getElementById('eyes-count').value = eyesCount;
            document.getElementById('total-rent').value = totalRent;
        }

        function addContract() {
            const contractNumber = document.getElementById('contract-number').value.trim();
            const supplierSelect = document.getElementById('supplier-select');
            const supplierId = supplierSelect.value;
            const supplierName = supplierSelect.options[supplierSelect.selectedIndex].text;
            const tablesCount = parseInt(document.getElementById('tables-count').value);

            if (!contractNumber || !supplierId || !tablesCount) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // Check if contract number already exists
            if (contracts.some(contract => contract.contractNumber === contractNumber)) {
                alert('رقم العقد موجود مسبقاً');
                return;
            }

            const eyesCount = tablesCount * 4;
            const totalRent = eyesCount * 60;

            const newContract = {
                id: Date.now(),
                contractNumber,
                supplierId,
                supplierName,
                tablesCount,
                eyesCount,
                totalRent,
                date: new Date().toLocaleDateString('ar-SA')
            };

            contracts.push(newContract);
            saveData();
            displayContracts();
            clearContractForm();
            alert('تم إضافة العقد بنجاح');
        }

        function clearContractForm() {
            document.getElementById('contract-number').value = '';
            document.getElementById('supplier-select').value = '';
            document.getElementById('tables-count').value = '';
            document.getElementById('eyes-count').value = '';
            document.getElementById('total-rent').value = '';
        }

        function displayContracts() {
            const tbody = document.getElementById('contracts-table-body');
            tbody.innerHTML = '';

            contracts.forEach(contract => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${contract.contractNumber}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${contract.supplierName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${contract.tablesCount}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${contract.eyesCount}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${contract.totalRent} د.ك</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium no-print">
                        <button onclick="deleteContract(${contract.id})" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function deleteContract(id) {
            if (confirm('هل أنت متأكد من حذف هذا العقد؟')) {
                contracts = contracts.filter(contract => contract.id !== id);
                saveData();
                displayContracts();
                alert('تم حذف العقد بنجاح');
            }
        }

        // Supplier management functions
        function addSupplier() {
            const name = document.getElementById('supplier-name').value.trim();
            const phone = document.getElementById('supplier-phone').value.trim();
            const email = document.getElementById('supplier-email').value.trim();
            const address = document.getElementById('supplier-address').value.trim();

            if (!name) {
                alert('يرجى إدخال اسم المورد');
                return;
            }

            const newSupplier = {
                id: Date.now(),
                name,
                phone,
                email,
                address,
                date: new Date().toLocaleDateString('ar-SA')
            };

            suppliers.push(newSupplier);
            saveData();
            displaySuppliers();
            updateSupplierSelect();
            clearSupplierForm();
            alert('تم إضافة المورد بنجاح');
        }

        function clearSupplierForm() {
            document.getElementById('supplier-name').value = '';
            document.getElementById('supplier-phone').value = '';
            document.getElementById('supplier-email').value = '';
            document.getElementById('supplier-address').value = '';
        }

        function displaySuppliers() {
            const tbody = document.getElementById('suppliers-table-body');
            tbody.innerHTML = '';

            suppliers.forEach(supplier => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${supplier.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${supplier.phone || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${supplier.email || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${supplier.address || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium no-print">
                        <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                suppliers = suppliers.filter(supplier => supplier.id !== id);
                saveData();
                displaySuppliers();
                updateSupplierSelect();
                alert('تم حذف المورد بنجاح');
            }
        }

        function updateSupplierSelect() {
            const select = document.getElementById('supplier-select');
            select.innerHTML = '<option value="">اختر المورد</option>';

            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
        }

        function loadSpiceSuppliers() {
            if (confirm('هل تريد تحميل قائمة موردين البهارات؟ سيتم إضافة ' + spiceSuppliersData.length + ' مورد جديد.')) {
                spiceSuppliersData.forEach(spiceSupplier => {
                    const newSupplier = {
                        id: Date.now() + Math.random(),
                        name: spiceSupplier.supplier_name,
                        phone: spiceSupplier.supplier_number,
                        email: '',
                        address: spiceSupplier.notes,
                        date: new Date().toLocaleDateString('ar-SA')
                    };
                    suppliers.push(newSupplier);
                });

                saveData();
                displaySuppliers();
                updateSupplierSelect();
                alert('تم تحميل موردين البهارات بنجاح');
            }
        }

        function exportSuppliersToJSON() {
            const dataStr = JSON.stringify(suppliers, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'suppliers_' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            URL.revokeObjectURL(url);
        }

        function importSuppliers(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const importedSuppliers = JSON.parse(e.target.result);
                    if (Array.isArray(importedSuppliers)) {
                        suppliers = [...suppliers, ...importedSuppliers];
                        saveData();
                        displaySuppliers();
                        updateSupplierSelect();
                        alert('تم استيراد الموردين بنجاح');
                    } else {
                        alert('تنسيق الملف غير صحيح');
                    }
                } catch (error) {
                    alert('خطأ في قراءة الملف');
                }
            };
            reader.readAsText(file);
        }

        // Spice suppliers functions
        function initializeSpiceSuppliers() {
            filteredSpiceSuppliers = [...spiceSuppliersData];
            displaySpiceSuppliers();
            updateSpiceStatistics();
            setupSpiceSupplierFilters();
        }

        function getSpiceSupplierStatus(notes) {
            if (notes.includes('عقد')) return 'عقد';
            if (notes.includes('نسبة')) return 'نسبة';
            if (notes.includes('موقوف')) return 'موقوف';
            return 'لم يورد';
        }

        function displaySpiceSuppliers() {
            const tbody = document.getElementById('spice-suppliers-table-body');
            tbody.innerHTML = '';

            filteredSpiceSuppliers.forEach((supplier, index) => {
                const status = getSpiceSupplierStatus(supplier.notes);
                const statusClass = status === 'عقد' ? 'status-contract' :
                    status === 'نسبة' ? 'status-percentage' :
                        status === 'موقوف' ? 'status-suspended' : 'status-not-supplied';

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${supplier.supplier_number}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${supplier.supplier_name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge ${statusClass}">${status}</span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-700">${supplier.notes}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateSpiceStatistics() {
            const stats = {
                contract: 0,
                percentage: 0,
                suspended: 0,
                notSupplied: 0
            };

            filteredSpiceSuppliers.forEach(supplier => {
                const status = getSpiceSupplierStatus(supplier.notes);
                if (status === 'عقد') stats.contract++;
                else if (status === 'نسبة') stats.percentage++;
                else if (status === 'موقوف') stats.suspended++;
                else stats.notSupplied++;
            });

            document.getElementById('spice-contract-count').textContent = stats.contract;
            document.getElementById('spice-percentage-count').textContent = stats.percentage;
            document.getElementById('spice-suspended-count').textContent = stats.suspended;
            document.getElementById('spice-not-supplied-count').textContent = stats.notSupplied;
        }

        function setupSpiceSupplierFilters() {
            const searchInput = document.getElementById('spice-search');
            const statusFilter = document.getElementById('spice-status-filter');

            function filterSpiceSuppliers() {
                const searchTerm = searchInput.value.toLowerCase();
                const statusFilter_value = statusFilter.value;

                filteredSpiceSuppliers = spiceSuppliersData.filter(supplier => {
                    const matchesSearch = supplier.supplier_name.toLowerCase().includes(searchTerm) ||
                        supplier.supplier_number.includes(searchTerm);
                    const status = getSpiceSupplierStatus(supplier.notes);
                    const matchesStatus = !statusFilter_value || status === statusFilter_value;

                    return matchesSearch && matchesStatus;
                });

                displaySpiceSuppliers();
                updateSpiceStatistics();
            }

            searchInput.addEventListener('input', filterSpiceSuppliers);
            statusFilter.addEventListener('change', filterSpiceSuppliers);
        }

        function exportSpiceSuppliersToExcel() {
            let csvContent = '\uFEFF'; // UTF-8 BOM
            csvContent += 'رقم المورد,اسم الشركة,الحالة,الملاحظات\n';

            filteredSpiceSuppliers.forEach(supplier => {
                const status = getSpiceSupplierStatus(supplier.notes);
                csvContent += `"${supplier.supplier_number}","${supplier.supplier_name}","${status}","${supplier.notes}"\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'spice_suppliers_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function printSpiceSuppliers() {
            window.print();
        }

        function importSpiceSuppliersToMain() {
            if (confirm('هل تريد إضافة موردي البهارات إلى النظام الرئيسي؟')) {
                localStorage.setItem('importedSpiceSuppliers', JSON.stringify(spiceSuppliersData));
                alert('تم حفظ موردي البهارات. يمكنك الآن الانتقال إلى تبويب إدارة الموردين واستخدام زر "تحميل موردين البهارات".');
            }
        }

        // Market report functions
        function initializeMarketReport() {
            filteredMarketData = [...marketReportData];
            displayMarketReport();
            updateMarketStatistics();
            setupMarketFilters();
        }

        const categories = {
            'beverages': 'المرطبات والمشروبات',
            'food': 'المواد الغذائية',
            'cosmetics': 'العطور والتجميل',
            'pharmaceuticals': 'الأدوية والكيماويات',
            'general-trade': 'التجارة العامة',
            'others': 'أخرى'
        };

        function displayMarketReport() {
            const tbody = document.getElementById('market-report-table-body');
            tbody.innerHTML = '';

            filteredMarketData.forEach((company, index) => {
                const categoryName = categories[company.category] || 'أخرى';
                const categoryClass = company.category;

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors duration-200';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${company.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="category-badge ${categoryClass}">${categoryName}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${company.utilized_space}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${company.contracted_space || '-'}</td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('market-displayed-companies').textContent = filteredMarketData.length;
        }

        function updateMarketStatistics() {
            const stats = {
                beverages: 0,
                food: 0,
                cosmetics: 0,
                pharmaceuticals: 0,
                'general-trade': 0,
                others: 0
            };

            filteredMarketData.forEach(company => {
                if (stats.hasOwnProperty(company.category)) {
                    stats[company.category]++;
                } else {
                    stats.others++;
                }
            });

            document.getElementById('market-beverages-count').textContent = stats.beverages;
            document.getElementById('market-food-count').textContent = stats.food;
            document.getElementById('market-cosmetics-count').textContent = stats.cosmetics;
            document.getElementById('market-pharmaceuticals-count').textContent = stats.pharmaceuticals;
            document.getElementById('market-general-trade-count').textContent = stats['general-trade'];
            document.getElementById('market-others-count').textContent = stats.others;
        }

        function setupMarketFilters() {
            const searchInput = document.getElementById('market-search');
            const categoryFilter = document.getElementById('market-category-filter');

            function filterMarketData() {
                const searchTerm = searchInput.value.toLowerCase();
                const categoryFilter_value = categoryFilter.value;

                filteredMarketData = marketReportData.filter(company => {
                    const matchesSearch = company.name.toLowerCase().includes(searchTerm);
                    const matchesCategory = !categoryFilter_value || company.category === categoryFilter_value;

                    return matchesSearch && matchesCategory;
                });

                displayMarketReport();
                updateMarketStatistics();
            }

            searchInput.addEventListener('input', filterMarketData);
            categoryFilter.addEventListener('change', filterMarketData);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize tables count calculation
            document.getElementById('tables-count').addEventListener('input', calculateValues);

            // Load initial data
            displayContracts();
            displaySuppliers();
            updateSupplierSelect();

            // Initialize first tab
            showTab('contracts');
        });
    </script>
</body>

</html>