/* Additional custom styles for the Contract Management System */

/* RTL Support */
* {
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON>l, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Custom animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hover effects */
.hover-scale {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Tab styling */
.tab-button {
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-button.active {
    background-color: rgba(59, 130, 246, 0.1);
}

.tab-button:hover:not(.active) {
    background-color: rgba(59, 130, 246, 0.05);
    border-bottom-color: rgba(59, 130, 246, 0.3);
}

/* Form styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
    background-color: #f9fafb;
    color: #6b7280;
}

/* Button styling */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* Table styling */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.data-table th {
    background-color: #f9fafb;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    color: #374151;
}

.data-table tr:hover {
    background-color: #f9fafb;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* Statistics cards */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stat-card-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-card-green {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
}

.stat-card-yellow {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Modal styling */
.modal-overlay {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    animation: slideIn 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

/* Search input styling */
.search-input {
    position: relative;
}

.search-input::before {
    content: '\f002';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    pointer-events: none;
}

.search-input input {
    padding-left: 2.5rem;
}

/* Action buttons */
.action-btn {
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.action-btn:hover {
    transform: scale(1.1);
}

.action-btn-edit {
    color: #3b82f6;
}

.action-btn-edit:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.action-btn-delete {
    color: #ef4444;
}

.action-btn-delete:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

.action-btn-view {
    color: #10b981;
}

.action-btn-view:hover {
    background-color: rgba(16, 185, 129, 0.1);
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .data-table {
        font-size: 0.875rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .modal-content {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
}

@media (max-width: 640px) {
    .grid-cols-1 {
        grid-template-columns: 1fr;
    }
    
    .md\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
    
    .md\\:grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .flex {
        flex-direction: column;
    }
    
    .space-x-4 > * + * {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .data-table {
        border: 1px solid #000;
    }
    
    .data-table th,
    .data-table td {
        border: 1px solid #000;
        padding: 0.5rem;
    }
    
    body {
        font-size: 12px;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus styles for accessibility */
.form-input:focus,
.btn:focus,
.tab-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Error states */
.form-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success states */
.form-input.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.success-message {
    color: #10b981;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
